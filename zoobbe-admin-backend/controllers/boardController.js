const Board = require('../models/Board');
const Workspace = require('../models/Workspace');
const Card = require('../models/Card');

// Get all boards with pagination, filtering, and search
const getBoards = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search, workspace, visibility } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};

    // Add status filter if provided
    if (status) {
      if (status === 'active') {
        query.archived = false;
      } else if (status === 'archived') {
        query.archived = true;
      }
    }

    // Add workspace filter if provided
    if (workspace) {
      // Try to find workspace by shortId first
      const workspaceObj = await Workspace.findOne({
        $or: [
          { shortId: workspace },
          { _id: workspace }
        ]
      });

      if (workspaceObj) {
        query.workspace = workspaceObj._id;
      }
    }

    // Add visibility filter if provided
    if (visibility) {
      query.visibility = visibility;
    }

    // Add search filter if provided
    if (search) {
      query.title = { $regex: search, $options: 'i' };
    }

    console.log('Query:', JSON.stringify(query));

    // Execute query with pagination
    const boards = await Board.find(query)
      .populate('workspace', 'name shortId')
      .populate('members.user', '_id name email username')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Board.countDocuments(query);

    // Get cards count for each board
    const boardsWithCardCount = await Promise.all(
      boards.map(async (board) => {
        const cardsCount = await Card.countDocuments({ board: board.shortId });
        return {
          ...board.toObject(),
          cardsCount
        };
      })
    );

    // Return boards with pagination info
    res.status(200).json({
      boards: boardsWithCardCount,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit), // Add totalPages for frontend
        pages: Math.ceil(total / limit) // Keep pages for backward compatibility
      }
    });
  } catch (error) {
    console.error('Error getting boards:', error);
    res.status(500).json({ message: 'Error getting boards' });
  }
};

// Get board by ID
const getBoardById = async (req, res) => {
  try {
    const { boardId } = req.params;

    // Find board by shortId
    const board = await Board.findOne({ shortId: boardId })
      .populate('workspace', 'name shortId')
      .populate('members.user', '_id name email username');

    if (!board) {
      // If not found by shortId, try finding by _id
      const boardById = await Board.findById(boardId)
        .populate('workspace', 'name shortId')
        .populate('members.user', '_id name email username');

      if (!boardById) {
        return res.status(404).json({ message: 'Board not found' });
      }

      // Get cards count
      const cardsCount = await Card.countDocuments({ board: boardById._id });

      // Return board with additional info
      return res.status(200).json({
        board: boardById,
        cardsCount
      });
    }

    // Get cards count
    const cardsCount = await Card.countDocuments({ board: board.shortId });

    // Get cards for this board
    const cards = await Card.find({ board: board.shortId })
      .limit(10)
      .sort({ createdAt: -1 });

    // Return board with additional info
    res.status(200).json({
      board,
      cardsCount,
      recentCards: cards
    });
  } catch (error) {
    console.error('Error getting board:', error);
    res.status(500).json({ message: 'Error getting board' });
  }
};

// Update board status (archive/unarchive)
const updateBoardStatus = async (req, res) => {
  try {
    const { boardId } = req.params;
    const { status } = req.body;

    if (!status || !['active', 'archived'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }

    // Find board by shortId
    let board = await Board.findOne({ shortId: boardId });

    // If not found by shortId, try finding by _id
    if (!board) {
      board = await Board.findById(boardId);

      if (!board) {
        return res.status(404).json({ message: 'Board not found' });
      }
    }

    // Update board status
    board.archived = status === 'archived';
    await board.save();

    // Populate board data
    await board.populate('workspace', 'name shortId');
    await board.populate('members.user', '_id name email username');

    res.status(200).json({
      message: `Board ${status === 'archived' ? 'archived' : 'activated'} successfully`,
      board
    });
  } catch (error) {
    console.error('Error updating board status:', error);
    res.status(500).json({ message: 'Error updating board status' });
  }
};

// Update board details
const updateBoardDetails = async (req, res) => {
  try {
    const { boardId } = req.params;
    const { title, visibility } = req.body;

    // Find board by shortId
    let board = await Board.findOne({ shortId: boardId });

    // If not found by shortId, try finding by _id
    if (!board) {
      board = await Board.findById(boardId);

      if (!board) {
        return res.status(404).json({ message: 'Board not found' });
      }
    }

    // Update board fields
    if (title) board.title = title;
    if (visibility) board.visibility = visibility;

    // Save the updated board
    await board.save();

    // Populate board data
    await board.populate('workspace', 'name shortId');
    await board.populate('members.user', '_id name email username');

    res.status(200).json({
      message: 'Board updated successfully',
      board
    });
  } catch (error) {
    console.error('Error updating board details:', error);
    res.status(500).json({ message: 'Error updating board details' });
  }
};

// Transfer board to another workspace
const transferBoard = async (req, res) => {
  try {
    const { boardId } = req.params;
    const { workspaceId } = req.body;

    if (!workspaceId) {
      return res.status(400).json({ message: 'Workspace ID is required' });
    }

    // Find board by shortId
    let board = await Board.findOne({ shortId: boardId });

    // If not found by shortId, try finding by _id
    if (!board) {
      board = await Board.findById(boardId);

      if (!board) {
        return res.status(404).json({ message: 'Board not found' });
      }
    }

    // Find target workspace
    const workspace = await Workspace.findOne({
      $or: [
        { shortId: workspaceId },
        { _id: workspaceId }
      ]
    });

    if (!workspace) {
      return res.status(404).json({ message: 'Target workspace not found' });
    }

    // Update board's workspace
    board.workspace = workspace._id;
    board.wShortId = workspace.shortId;

    // Save the updated board
    await board.save();

    // Populate board data
    await board.populate('workspace', 'name shortId');
    await board.populate('members.user', '_id name email username');

    res.status(200).json({
      message: 'Board transferred successfully',
      board
    });
  } catch (error) {
    console.error('Error transferring board:', error);
    res.status(500).json({ message: 'Error transferring board' });
  }
};

// Get board members
const getBoardMembers = async (req, res) => {
  try {
    const { boardId } = req.params;

    // Find board by shortId
    let board = await Board.findOne({ shortId: boardId });

    // If not found by shortId, try finding by _id
    if (!board) {
      board = await Board.findById(boardId);

      if (!board) {
        return res.status(404).json({ message: 'Board not found' });
      }
    }

    // Populate members data
    await board.populate('members.user', '_id name email username profilePicture');

    res.status(200).json({
      members: board.members
    });
  } catch (error) {
    console.error('Error getting board members:', error);
    res.status(500).json({ message: 'Error getting board members' });
  }
};

// Update board member role
const updateBoardMemberRole = async (req, res) => {
  try {
    const { boardId, userId } = req.params;
    const { role } = req.body;

    if (!role || !['admin', 'member'].includes(role)) {
      return res.status(400).json({ message: 'Invalid role' });
    }

    // Find board by shortId
    let board = await Board.findOne({ shortId: boardId });

    // If not found by shortId, try finding by _id
    if (!board) {
      board = await Board.findById(boardId);

      if (!board) {
        return res.status(404).json({ message: 'Board not found' });
      }
    }

    // Find the member in the board
    const memberIndex = board.members.findIndex(
      member => member.user.toString() === userId
    );

    if (memberIndex === -1) {
      return res.status(404).json({ message: 'Member not found in this board' });
    }

    // Update member role
    board.members[memberIndex].role = role;

    // Save the updated board
    await board.save();

    // Populate members data
    await board.populate('members.user', '_id name email username');

    res.status(200).json({
      message: 'Member role updated successfully',
      members: board.members
    });
  } catch (error) {
    console.error('Error updating member role:', error);
    res.status(500).json({ message: 'Error updating member role' });
  }
};

// Remove member from board
const removeBoardMember = async (req, res) => {
  try {
    const { boardId, userId } = req.params;

    // Find board by shortId
    let board = await Board.findOne({ shortId: boardId });

    // If not found by shortId, try finding by _id
    if (!board) {
      board = await Board.findById(boardId);

      if (!board) {
        return res.status(404).json({ message: 'Board not found' });
      }
    }

    // Find the member in the board
    const memberIndex = board.members.findIndex(
      member => member.user.toString() === userId
    );

    if (memberIndex === -1) {
      return res.status(404).json({ message: 'Member not found in this board' });
    }

    // Remove member from board
    board.members.splice(memberIndex, 1);

    // Save the updated board
    await board.save();

    // Populate members data
    await board.populate('members.user', '_id name email username');

    res.status(200).json({
      message: 'Member removed successfully',
      members: board.members
    });
  } catch (error) {
    console.error('Error removing board member:', error);
    res.status(500).json({ message: 'Error removing board member' });
  }
};

module.exports = {
  getBoards,
  getBoardById,
  updateBoardStatus,
  updateBoardDetails,
  transferBoard,
  getBoardMembers,
  updateBoardMemberRole,
  removeBoardMember
};
