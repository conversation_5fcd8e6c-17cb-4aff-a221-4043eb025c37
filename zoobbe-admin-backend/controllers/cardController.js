const mongoose = require('mongoose');
const Card = require('../models/Card');
const Board = require('../models/Board');

// Get all cards with pagination, filtering, and search
const getCards = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search, board, priority } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};

    // Add status filter if provided
    if (status) {
      if (status === 'active') {
        query.archived = false;
      } else if (status === 'archived') {
        query.archived = true;
      }
    }

    // Add board filter if provided
    if (board) {
      // Try to find the board by shortId
      const boardObj = await Board.findOne({ shortId: board });
      if (boardObj) {
        // Use the shortId directly since that's what's stored in the Card model
        query.board = board;
      }
    }

    // Add priority filter if provided
    if (priority) {
      query['priority.value'] = priority;
    }

    // Add search filter if provided
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const cards = await Card.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Manually populate board data since board is stored as shortId
    const populatedCards = await Promise.all(
      cards.map(async (card) => {
        const cardObj = card.toObject();

        // Populate board data
        if (cardObj.board) {
          const boardData = await Board.findOne({ shortId: cardObj.board });
          if (boardData) {
            cardObj.board = {
              _id: boardData._id,
              title: boardData.title,
              shortId: boardData.shortId
            };
          }
        }

        // Populate user data
        if (cardObj.users && cardObj.users.length > 0) {
          const userIds = cardObj.users.filter(id => mongoose.Types.ObjectId.isValid(id));
          if (userIds.length > 0) {
            const users = await mongoose.model('User').find({ _id: { $in: userIds } }, '_id name email username');
            cardObj.users = users;
          } else {
            cardObj.users = [];
          }
        }

        return cardObj;
      })
    );

    // Get total count for pagination
    const total = await Card.countDocuments(query);

    // Return populated cards with pagination info
    res.status(200).json({
      cards: populatedCards,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit), // Add totalPages for frontend
        pages: Math.ceil(total / limit) // Keep pages for backward compatibility
      }
    });
  } catch (error) {
    console.error('Error getting cards:', error);
    res.status(500).json({ message: 'Error getting cards' });
  }
};

// Get card by ID
const getCardById = async (req, res) => {
  try {
    const { cardId } = req.params;

    // Find card
    const card = await Card.findOne({ shortId: cardId })
      .populate('actionList', 'title');

    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    // Convert to plain object for manipulation
    const cardObj = card.toObject();

    // Manually populate board data
    if (cardObj.board) {
      const boardData = await Board.findOne({ shortId: cardObj.board });
      if (boardData) {
        cardObj.board = {
          _id: boardData._id,
          title: boardData.title,
          shortId: boardData.shortId
        };
      }
    }

    // Manually populate user data
    if (cardObj.users && cardObj.users.length > 0) {
      const userIds = cardObj.users.filter(id => mongoose.Types.ObjectId.isValid(id));
      if (userIds.length > 0) {
        const users = await mongoose.model('User').find({ _id: { $in: userIds } }, '_id name email username');
        cardObj.users = users;
      } else {
        cardObj.users = [];
      }
    }

    res.status(200).json({ card: cardObj });
  } catch (error) {
    console.error('Error getting card:', error);
    res.status(500).json({ message: 'Error getting card' });
  }
};

// Update card status (archive/unarchive)
const updateCardStatus = async (req, res) => {
  try {
    const { cardId } = req.params;
    const { status } = req.body;

    if (!status || !['active', 'archived'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }

    // Find card
    const card = await Card.findOne({ shortId: cardId });
    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    // Update card status
    card.archived = status === 'archived';
    await card.save();

    res.status(200).json({
      message: `Card ${status === 'archived' ? 'archived' : 'activated'} successfully`,
      card
    });
  } catch (error) {
    console.error('Error updating card status:', error);
    res.status(500).json({ message: 'Error updating card status' });
  }
};

module.exports = {
  getCards,
  getCardById,
  updateCardStatus
};
