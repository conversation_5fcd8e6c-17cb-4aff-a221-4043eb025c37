const FeatureFlag = require('../models/FeatureFlag');
const User = require('../models/User');
const Workspace = require('../models/Workspace');
const Activity = require('../models/Activity');

// Get all feature flags
const getFeatureFlags = async (req, res) => {
  try {
    const { enabled, experimental } = req.query;
    
    // Build query
    let query = {};
    
    if (enabled !== undefined) {
      query.enabled = enabled === 'true';
    }
    
    if (experimental !== undefined) {
      query.isExperimental = experimental === 'true';
    }
    
    // Execute query
    const featureFlags = await FeatureFlag.find(query)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ createdAt: -1 });
    
    res.status(200).json({ featureFlags });
  } catch (error) {
    console.error('Error getting feature flags:', error);
    res.status(500).json({ message: 'Error getting feature flags' });
  }
};

// Get feature flag by ID
const getFeatureFlagById = async (req, res) => {
  try {
    const { flagId } = req.params;
    
    // Find feature flag
    const featureFlag = await FeatureFlag.findById(flagId)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .populate('targetUsers', 'name email username')
      .populate('targetWorkspaces', 'name shortName');
    
    if (!featureFlag) {
      return res.status(404).json({ message: 'Feature flag not found' });
    }
    
    res.status(200).json({ featureFlag });
  } catch (error) {
    console.error('Error getting feature flag:', error);
    res.status(500).json({ message: 'Error getting feature flag' });
  }
};

// Create feature flag
const createFeatureFlag = async (req, res) => {
  try {
    const { 
      name, 
      description, 
      enabled, 
      rolloutPercentage, 
      targetUsers, 
      targetWorkspaces, 
      targetPlans,
      isExperimental,
      startDate,
      endDate
    } = req.body;
    const adminId = req.admin.id;
    
    // Validate input
    if (!name) {
      return res.status(400).json({ message: 'Name is required' });
    }
    
    // Check if feature flag already exists
    const existingFlag = await FeatureFlag.findOne({ name });
    if (existingFlag) {
      return res.status(400).json({ message: 'Feature flag with this name already exists' });
    }
    
    // Create feature flag
    const featureFlag = new FeatureFlag({
      name,
      description,
      enabled: enabled || false,
      rolloutPercentage: rolloutPercentage || 0,
      targetUsers: targetUsers || [],
      targetWorkspaces: targetWorkspaces || [],
      targetPlans: targetPlans || [],
      isExperimental: isExperimental !== undefined ? isExperimental : true,
      startDate: startDate ? new Date(startDate) : null,
      endDate: endDate ? new Date(endDate) : null,
      createdBy: adminId,
      updatedBy: adminId
    });
    
    await featureFlag.save();
    
    // Log activity
    await Activity.create({
      user: adminId,
      action: 'FEATURE_FLAG_CREATED',
      targetType: 'FEATURE_FLAG',
      target: featureFlag._id,
      metadata: {
        name: featureFlag.name,
        enabled: featureFlag.enabled
      }
    });
    
    res.status(201).json({
      message: 'Feature flag created successfully',
      featureFlag
    });
  } catch (error) {
    console.error('Error creating feature flag:', error);
    res.status(500).json({ message: 'Error creating feature flag' });
  }
};

// Update feature flag
const updateFeatureFlag = async (req, res) => {
  try {
    const { flagId } = req.params;
    const { 
      name, 
      description, 
      enabled, 
      rolloutPercentage, 
      targetUsers, 
      targetWorkspaces, 
      targetPlans,
      isExperimental,
      startDate,
      endDate
    } = req.body;
    const adminId = req.admin.id;
    
    // Find feature flag
    const featureFlag = await FeatureFlag.findById(flagId);
    if (!featureFlag) {
      return res.status(404).json({ message: 'Feature flag not found' });
    }
    
    // Update fields
    if (name) featureFlag.name = name;
    if (description !== undefined) featureFlag.description = description;
    if (enabled !== undefined) featureFlag.enabled = enabled;
    if (rolloutPercentage !== undefined) featureFlag.rolloutPercentage = rolloutPercentage;
    if (targetUsers) featureFlag.targetUsers = targetUsers;
    if (targetWorkspaces) featureFlag.targetWorkspaces = targetWorkspaces;
    if (targetPlans) featureFlag.targetPlans = targetPlans;
    if (isExperimental !== undefined) featureFlag.isExperimental = isExperimental;
    if (startDate) featureFlag.startDate = new Date(startDate);
    if (endDate) featureFlag.endDate = new Date(endDate);
    
    featureFlag.updatedBy = adminId;
    featureFlag.updatedAt = new Date();
    
    await featureFlag.save();
    
    // Log activity
    await Activity.create({
      user: adminId,
      action: 'FEATURE_FLAG_UPDATED',
      targetType: 'FEATURE_FLAG',
      target: featureFlag._id,
      metadata: {
        name: featureFlag.name,
        enabled: featureFlag.enabled
      }
    });
    
    res.status(200).json({
      message: 'Feature flag updated successfully',
      featureFlag
    });
  } catch (error) {
    console.error('Error updating feature flag:', error);
    res.status(500).json({ message: 'Error updating feature flag' });
  }
};

// Delete feature flag
const deleteFeatureFlag = async (req, res) => {
  try {
    const { flagId } = req.params;
    const adminId = req.admin.id;
    
    // Find feature flag
    const featureFlag = await FeatureFlag.findById(flagId);
    if (!featureFlag) {
      return res.status(404).json({ message: 'Feature flag not found' });
    }
    
    // Store flag name for activity log
    const flagName = featureFlag.name;
    
    // Delete feature flag
    await featureFlag.remove();
    
    // Log activity
    await Activity.create({
      user: adminId,
      action: 'FEATURE_FLAG_DELETED',
      targetType: 'FEATURE_FLAG',
      metadata: {
        name: flagName
      }
    });
    
    res.status(200).json({
      message: 'Feature flag deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting feature flag:', error);
    res.status(500).json({ message: 'Error deleting feature flag' });
  }
};

module.exports = {
  getFeatureFlags,
  getFeatureFlagById,
  createFeatureFlag,
  updateFeatureFlag,
  deleteFeatureFlag
};
