const Subscription = require('../models/Subscription');
const User = require('../models/User');
const Workspace = require('../models/Workspace');
const Activity = require('../models/Activity');

// Get all subscriptions with pagination, filtering, and search
const getSubscriptions = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, plan, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};

    // Add status filter if provided
    if (status) {
      query.status = status;
    }

    // Add plan filter if provided
    if (plan) {
      query.plan = plan;
    }

    // Add search filter if provided
    if (search) {
      // We need to find users that match the search and then find subscriptions for those users
      const users = await User.find({
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { username: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      const userIds = users.map(user => user._id);
      query.user = { $in: userIds };
    }

    // Execute query with pagination
    const subscriptions = await Subscription.find(query)
      .populate('user', '_id name email username')
      .populate('workspace', '_id name shortName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Subscription.countDocuments(query);

    // Return subscriptions with pagination info
    res.status(200).json({
      subscriptions,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting subscriptions:', error);
    res.status(500).json({ message: 'Error getting subscriptions' });
  }
};

// Get subscription by ID
const getSubscriptionById = async (req, res) => {
  try {
    const { subscriptionId } = req.params;

    // Find subscription
    const subscription = await Subscription.findById(subscriptionId)
      .populate('user', '_id name email username')
      .populate('workspace', '_id name shortName');

    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    // Return subscription
    res.status(200).json({ subscription });
  } catch (error) {
    console.error('Error getting subscription:', error);
    res.status(500).json({ message: 'Error getting subscription' });
  }
};

// Update subscription status
const updateSubscriptionStatus = async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    const { status } = req.body;
    const adminId = req.admin.id;

    // Validate input
    if (!status) {
      return res.status(400).json({ message: 'Status is required' });
    }

    // Find subscription
    const subscription = await Subscription.findById(subscriptionId)
      .populate('user', '_id name email');

    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    // Update subscription status
    subscription.status = status;
    await subscription.save();

    // Update user plan status if needed
    if (status === 'canceled' || status === 'unpaid') {
      await User.findByIdAndUpdate(subscription.user._id, { 
        plan: 'free',
        isPremiumMember: false
      });
    } else if (status === 'active') {
      await User.findByIdAndUpdate(subscription.user._id, { 
        plan: subscription.plan,
        isPremiumMember: subscription.plan !== 'free'
      });
    }

    // Log activity
    await Activity.create({
      user: subscription.user._id,
      action: 'SUBSCRIPTION_STATUS_UPDATED',
      targetType: 'SUBSCRIPTION',
      target: subscriptionId,
      metadata: {
        adminId,
        oldStatus: subscription.status,
        newStatus: status
      }
    });

    // Return updated subscription
    res.status(200).json({
      message: 'Subscription status updated successfully',
      subscription
    });
  } catch (error) {
    console.error('Error updating subscription status:', error);
    res.status(500).json({ message: 'Error updating subscription status' });
  }
};

// Apply discount to subscription
const applyDiscount = async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    const { code, amount, type, expiresAt } = req.body;
    const adminId = req.admin.id;

    // Validate input
    if (!code || !amount || !type) {
      return res.status(400).json({ message: 'Discount code, amount, and type are required' });
    }

    // Find subscription
    const subscription = await Subscription.findById(subscriptionId)
      .populate('user', '_id name email');

    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    // Apply discount
    subscription.discount = {
      code,
      amount,
      type,
      expiresAt: expiresAt ? new Date(expiresAt) : null
    };
    await subscription.save();

    // Log activity
    await Activity.create({
      user: subscription.user._id,
      action: 'DISCOUNT_APPLIED',
      targetType: 'SUBSCRIPTION',
      target: subscriptionId,
      metadata: {
        adminId,
        discount: {
          code,
          amount,
          type,
          expiresAt
        }
      }
    });

    // Return updated subscription
    res.status(200).json({
      message: 'Discount applied successfully',
      subscription
    });
  } catch (error) {
    console.error('Error applying discount:', error);
    res.status(500).json({ message: 'Error applying discount' });
  }
};

// Get subscription plans
const getSubscriptionPlans = async (req, res) => {
  try {
    // Return subscription plans
    res.status(200).json({
      plans: [
        {
          id: 'free',
          name: 'Free',
          description: 'Basic features for individuals',
          price: 0,
          currency: 'usd',
          interval: 'month',
          features: [
            'Up to 3 boards',
            'Basic collaboration',
            'Standard support'
          ]
        },
        {
          id: 'standard',
          name: 'Standard',
          description: 'Enhanced features for small teams',
          price: 9.99,
          currency: 'usd',
          interval: 'month',
          features: [
            'Unlimited boards',
            'Advanced collaboration',
            'Priority support',
            'File attachments up to 100MB'
          ]
        },
        {
          id: 'premium',
          name: 'Premium',
          description: 'Advanced features for growing teams',
          price: 19.99,
          currency: 'usd',
          interval: 'month',
          features: [
            'All Standard features',
            'Advanced analytics',
            'Custom fields',
            'File attachments up to 1GB',
            'Premium support'
          ]
        },
        {
          id: 'enterprise',
          name: 'Enterprise',
          description: 'Complete solution for large organizations',
          price: 49.99,
          currency: 'usd',
          interval: 'month',
          features: [
            'All Premium features',
            'SSO integration',
            'Advanced security',
            'Dedicated account manager',
            'Custom integrations',
            'Unlimited storage'
          ]
        }
      ]
    });
  } catch (error) {
    console.error('Error getting subscription plans:', error);
    res.status(500).json({ message: 'Error getting subscription plans' });
  }
};

module.exports = {
  getSubscriptions,
  getSubscriptionById,
  updateSubscriptionStatus,
  applyDiscount,
  getSubscriptionPlans
};
