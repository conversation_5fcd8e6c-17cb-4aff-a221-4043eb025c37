const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const adminSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['super-admin', 'admin', 'moderator'],
    default: 'admin'
  },
  permissions: [{
    type: String,
    enum: [
      'manage_users',
      'manage_workspaces',
      'manage_boards',
      'manage_cards',
      'manage_subscriptions',
      'manage_feature_flags',
      'manage_feedback',
      'manage_system_settings',
      'view_stats',
      'view_activity',
      'view_logs',
      'manage_admins'
    ]
  }],
  lastLogin: {
    type: Date
  },
  active: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Hash password before saving
adminSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
adminSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Set default permissions based on role
adminSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('role')) {
    switch (this.role) {
      case 'super-admin':
        this.permissions = [
          'manage_users',
          'manage_workspaces',
          'manage_boards',
          'manage_cards',
          'manage_subscriptions',
          'manage_feature_flags',
          'manage_feedback',
          'manage_system_settings',
          'view_stats',
          'view_activity',
          'view_logs',
          'manage_admins'
        ];
        break;
      case 'admin':
        this.permissions = [
          'manage_users',
          'manage_workspaces',
          'manage_boards',
          'manage_cards',
          'manage_subscriptions',
          'view_stats',
          'view_activity',
          'view_logs'
        ];
        break;
      case 'moderator':
        this.permissions = [
          'view_stats',
          'view_activity'
        ];
        break;
    }
  }
  next();
});

const Admin = mongoose.model('Admin', adminSchema);

module.exports = Admin;
