const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define a simplified version of the Board schema that matches the main application
const boardSchema = new Schema({
  title: { type: String, required: true },
  actionLists: [{ type: Schema.Types.ObjectId, ref: 'ActionList' }],
  workspace: { type: Schema.Types.ObjectId, ref: 'Workspace' },
  wShortId: { type: String, ref: 'Workspace' },
  importId: { type: String, required: false },
  archived: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  lastViewed: { type: Date, default: Date.now },
  members: [{
    user: { type: Schema.Types.ObjectId, ref: 'User' },
    role: { type: String, default: 'member' },
    workspaceRole: { type: String, default: 'member' },
    status: { type: String, enum: ['pending', 'accepted'], default: 'pending' }
  }],
  labels: [{
    text: { type: String },
    color: { type: String }
  }],
  attachments: [{
    name: { type: String },
    url: { type: String },
    signedUrl: { type: String },
    uploadedAt: { type: Date },
    boardId: { type: Schema.Types.Mixed, ref: 'Board' },
    fileHash: { type: Schema.Types.Mixed },
    expiresAt: { type: Date }
  }],
  cardsCount: { type: Number, default: null },
  cardNumberCounter: { type: Number, default: 0 },
  shortId: { type: String, unique: true, sparse: true },
  shortLink: { type: String },
  permalink: { type: String },
  type: { type: String, default: 'BOARD' },
  joinLink: { type: String, default: null },
  visibility: {
    type: String,
    enum: ['Private', 'Workspace', 'Public'],
    default: 'Private'
  },
  cover: {
    name: { type: String },
    url: { type: String },
    sizes: {
      original: { type: String },
      thumbnail: { type: String },
      medium: { type: String },
      large: { type: String },
    },
    boardId: { type: Schema.Types.Mixed, ref: 'Board' },
    coverColor: { type: Array },
    attachmentId: { type: String },
  }
}, {
  // This tells Mongoose not to create a new collection but use the existing one
  collection: 'boards'
});

// Create the Board model
// The third parameter ensures we're using the existing 'boards' collection
const Board = mongoose.model('Board', boardSchema, 'boards');

module.exports = Board;
