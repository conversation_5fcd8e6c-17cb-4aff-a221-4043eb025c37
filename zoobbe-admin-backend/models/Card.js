const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define a schema that matches the main application's Card model
const cardSchema = new Schema({
  title: { type: String, required: true },
  description: { type: String },
  actionList: { type: Schema.Types.ObjectId, ref: 'ActionList' },
  board: { type: Schema.Types.Mixed, ref: 'Board' },
  position: { type: Number, default: 0 },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  dueDate: { type: Date },
  users: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  labels: [{
    text: { type: String },
    color: { type: String }
  }],
  archived: { type: Boolean, default: false },
  attachments: [{
    name: { type: String },
    url: { type: String },
    signedUrl: { type: String },
    uploadedAt: { type: Date },
    cardId: { type: Schema.Types.Mixed, ref: 'Card' },
    fileHash: { type: Schema.Types.Mixed },
    expiresAt: { type: Date }
  }],
  comments: [{
    text: { type: String },
    user: { type: Schema.Types.ObjectId, ref: 'User' },
    createdAt: { type: Date, default: Date.now }
  }],
  priority: {
    value: { type: String, default: 'medium' },
    color: { type: String }
  },
  shortId: { type: String },
  cardNumber: { type: Number },
  checklists: [{ type: Schema.Types.ObjectId, ref: 'Checklist' }],
  cover: {
    name: { type: String },
    url: { type: String },
    sizes: {
      original: { type: String },
      thumbnail: { type: String },
      medium: { type: String },
      large: { type: String },
    },
    cardId: { type: Schema.Types.Mixed, ref: 'Card' },
    coverColor: { type: Array },
    attachmentId: { type: String },
  }
}, {
  // This tells Mongoose to use the existing 'cards' collection
  collection: 'cards'
});

// Create the Card model
// The third parameter ensures we're using the existing 'cards' collection
const Card = mongoose.model('Card', cardSchema, 'cards');

module.exports = Card;
