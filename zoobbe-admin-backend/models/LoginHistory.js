const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const loginHistorySchema = new Schema({
  user: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  timestamp: { 
    type: Date, 
    default: Date.now 
  },
  ip: { 
    type: String 
  },
  userAgent: { 
    type: String 
  },
  browser: { 
    type: String 
  },
  os: { 
    type: String 
  },
  device: { 
    type: String 
  },
  successful: { 
    type: Boolean, 
    default: true 
  },
  failureReason: { 
    type: String 
  },
  location: {
    country: { type: String },
    city: { type: String },
    coordinates: {
      latitude: { type: Number },
      longitude: { type: Number }
    }
  }
});

// Create indexes for better query performance
loginHistorySchema.index({ user: 1, timestamp: -1 });
loginHistorySchema.index({ timestamp: -1 });
loginHistorySchema.index({ successful: 1 });

const LoginHistory = mongoose.model('LoginHistory', loginHistorySchema);

module.exports = LoginHistory;
