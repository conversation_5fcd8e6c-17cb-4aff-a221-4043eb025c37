const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const subscriptionSchema = new Schema({
  user: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  workspace: { 
    type: Schema.Types.ObjectId, 
    ref: 'Workspace' 
  },
  plan: { 
    type: String, 
    enum: ['free', 'standard', 'premium', 'enterprise'], 
    required: true 
  },
  status: { 
    type: String, 
    enum: ['active', 'canceled', 'past_due', 'unpaid', 'trialing', 'incomplete'], 
    default: 'active' 
  },
  startDate: { 
    type: Date, 
    default: Date.now 
  },
  endDate: { 
    type: Date 
  },
  trialEndDate: { 
    type: Date 
  },
  cancelAtPeriodEnd: { 
    type: Boolean, 
    default: false 
  },
  stripeCustomerId: { 
    type: String 
  },
  stripeSubscriptionId: { 
    type: String 
  },
  stripePriceId: { 
    type: String 
  },
  lastPaymentDate: { 
    type: Date 
  },
  nextPaymentDate: { 
    type: Date 
  },
  paymentMethod: { 
    type: String 
  },
  amount: { 
    type: Number 
  },
  currency: { 
    type: String, 
    default: 'usd' 
  },
  interval: { 
    type: String, 
    enum: ['month', 'year'], 
    default: 'month' 
  },
  discount: {
    type: {
      code: { type: String },
      amount: { type: Number },
      type: { type: String, enum: ['percentage', 'fixed'] },
      expiresAt: { type: Date }
    }
  },
  metadata: { 
    type: Schema.Types.Mixed 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Create indexes for better query performance
subscriptionSchema.index({ user: 1 });
subscriptionSchema.index({ workspace: 1 });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ plan: 1 });
subscriptionSchema.index({ stripeSubscriptionId: 1 });

// Update the updatedAt field on save
subscriptionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const Subscription = mongoose.model('Subscription', subscriptionSchema);

module.exports = Subscription;
