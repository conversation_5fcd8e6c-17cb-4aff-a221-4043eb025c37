const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const systemSettingsSchema = new Schema({
  maintenanceMode: {
    enabled: { type: Boolean, default: false },
    message: { type: String, default: 'System is under maintenance. Please try again later.' },
    scheduledStart: { type: Date },
    scheduledEnd: { type: Date }
  },
  allowedDomains: [{ 
    type: String 
  }],
  maxFileSize: { 
    type: Number, 
    default: 10485760 // 10MB in bytes
  },
  maxFilesPerCard: { 
    type: Number, 
    default: 10 
  },
  maxBoardsPerWorkspace: {
    free: { type: Number, default: 3 },
    standard: { type: Number, default: 10 },
    premium: { type: Number, default: 50 },
    enterprise: { type: Number, default: 1000 }
  },
  maxMembersPerWorkspace: {
    free: { type: Number, default: 5 },
    standard: { type: Number, default: 20 },
    premium: { type: Number, default: 100 },
    enterprise: { type: Number, default: 1000 }
  },
  maxCardsPerBoard: {
    free: { type: Number, default: 50 },
    standard: { type: Number, default: 200 },
    premium: { type: Number, default: 1000 },
    enterprise: { type: Number, default: 10000 }
  },
  integrations: {
    slack: { enabled: { type: Boolean, default: true } },
    google: { enabled: { type: Boolean, default: true } },
    github: { enabled: { type: Boolean, default: true } },
    trello: { enabled: { type: Boolean, default: true } }
  },
  security: {
    passwordMinLength: { type: Number, default: 8 },
    passwordRequireSpecialChar: { type: Boolean, default: true },
    passwordRequireNumber: { type: Boolean, default: true },
    passwordRequireUppercase: { type: Boolean, default: true },
    maxLoginAttempts: { type: Number, default: 5 },
    lockoutDuration: { type: Number, default: 30 }, // minutes
    sessionTimeout: { type: Number, default: 60 }, // minutes
    requireEmailVerification: { type: Boolean, default: true }
  },
  email: {
    fromEmail: { type: String, default: '<EMAIL>' },
    supportEmail: { type: String, default: '<EMAIL>' },
    adminEmail: { type: String, default: '<EMAIL>' }
  },
  analytics: {
    googleAnalyticsId: { type: String },
    mixpanelToken: { type: String },
    hotjarId: { type: String }
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'Admin' 
  }
});

// Update the updatedAt field on save
systemSettingsSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const SystemSettings = mongoose.model('SystemSettings', systemSettingsSchema);

module.exports = SystemSettings;
