const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define a simplified version of the User schema that matches the main application
const userSchema = new Schema({
  name: { type: String },
  email: { type: String, required: true, unique: true },
  passwordHash: { type: String },
  username: { type: String, required: true, unique: true },
  profilePicture: { type: String },
  bio: { type: String },
  isDefaultPasswordSet: { type: Boolean, default: true },
  isPremiumMember: { type: Boolean, default: false },
  canSeeOnlineStatus: { type: Boolean, default: false },
  newNotificationsCount: { type: Number, default: 0 },
  createdAt: { type: Date, default: Date.now },
  workspaces: [{ type: Schema.Types.Mixed, ref: 'Workspace' }],
  guests: [{ type: Schema.Types.ObjectId, ref: 'Board' }],
  activities: [{ type: Schema.Types.ObjectId, ref: 'Activity' }],
  type: { type: String, default: 'USER' },
  verified: { type: Boolean, default: false },
  resetToken: { type: String, default: null },
  resetTokenExpiry: { type: Date, default: null },
  starredBoards: [{ type: Schema.Types.ObjectId, ref: 'Board' }],
  recentBoards: [{ type: Schema.Types.ObjectId, ref: 'Board' }],
  pushTokens: [{ type: String }],
  googleId: { type: String, sparse: true },
  registeredWithGoogle: { type: Boolean, default: false },
  online: { type: Boolean, default: false },
  lastActive: { type: Date, default: null },
  suspended: { type: Boolean, default: false },
  suspensionReason: { type: String },
  suspendedAt: { type: Date },
  suspendedBy: { type: Schema.Types.ObjectId, ref: 'Admin' },
  plan: {
    type: String,
    enum: ['free', 'standard', 'premium', 'enterprise'],
    default: 'free'
  },
  planStartDate: { type: Date },
  planEndDate: { type: Date },
  subscription: {
    type: Schema.Types.ObjectId,
    ref: 'Subscription'
  },
  lastLoginAt: { type: Date },
  lastLoginIp: { type: String },
  failedLoginAttempts: { type: Number, default: 0 },
  accountLockedUntil: { type: Date },
  featureFlags: [{
    feature: { type: String },
    enabled: { type: Boolean, default: false },
    expiresAt: { type: Date }
  }],
  notes: { type: String }, // Admin notes about the user
  referredBy: { type: Schema.Types.ObjectId, ref: 'User' },
  referralCode: { type: String },
  referralCount: { type: Number, default: 0 }
});

// Create the User model
const User = mongoose.model('User', userSchema);

module.exports = User;
