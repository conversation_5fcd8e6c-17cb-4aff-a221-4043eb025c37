const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define a simplified version of the Workspace schema that matches the main application
const workspaceSchema = new Schema({
  name: { type: String, required: true },
  shortName: { type: String },
  ownerId: { type: String, required: true, index: true },
  boards: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Board' }],
  members: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', index: true },
    role: { type: String, default: 'admin' },
    accessLevel: { type: String, enum: ['full', 'limited', 'view-only'], default: 'limited' },
  }],
  guests: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User', index: true }],
  type: { type: String, default: 'WORKSPACE' },
  description: { type: String },
  createdAt: { type: Date, default: Date.now },
  cardsCount: { type: Number, default: 0 },
  shortId: { type: String, sparse: true, index: true },
  shortLink: { type: String },
  permalink: { type: String },
  joinLink: { type: String, default: null },
  subscription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subscription'
  },
  status: { type: String, default: 'active' }
});

// Create the Workspace model
const Workspace = mongoose.model('Workspace', workspaceSchema);

module.exports = Workspace;
