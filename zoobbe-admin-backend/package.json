{"name": "zoobbe-admin-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "create-admin": "node scripts/createAdmin.js", "seed-data": "node scripts/seedData.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.3"}, "devDependencies": {"nodemon": "^3.1.10"}}