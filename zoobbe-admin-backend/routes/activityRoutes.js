const express = require('express');
const router = express.Router();
const { 
  getActivityLog, 
  getActivityTypes, 
  getActivityStats 
} = require('../controllers/activityController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get activity log
router.get('/', checkPermission('view_activity'), getActivityLog);

// Get activity types
router.get('/types', checkPermission('view_activity'), getActivityTypes);

// Get activity statistics
router.get('/stats', checkPermission('view_activity'), getActivityStats);

module.exports = router;
