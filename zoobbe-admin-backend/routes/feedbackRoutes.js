const express = require('express');
const router = express.Router();
const { 
  getFeedback, 
  getFeedbackById, 
  updateFeedbackStatus, 
  addFeedbackResponse, 
  addInternalNote, 
  assignFeedback 
} = require('../controllers/feedbackController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get all feedback
router.get('/', checkPermission('manage_feedback'), getFeedback);

// Get feedback by ID
router.get('/:feedbackId', checkPermission('manage_feedback'), getFeedbackById);

// Update feedback status
router.patch('/:feedbackId/status', checkPermission('manage_feedback'), updateFeedbackStatus);

// Add response to feedback
router.post('/:feedbackId/response', checkPermission('manage_feedback'), addFeedbackResponse);

// Add internal note to feedback
router.post('/:feedbackId/note', checkPermission('manage_feedback'), addInternalNote);

// Assign feedback to admin
router.post('/:feedbackId/assign', checkPermission('manage_feedback'), assignFeedback);

module.exports = router;
