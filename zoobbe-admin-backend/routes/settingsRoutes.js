const express = require('express');
const router = express.Router();
const { 
  getSystemSettings, 
  updateSystemSettings, 
  toggleMaintenanceMode, 
  updateAllowedDomains, 
  toggleIntegration 
} = require('../controllers/settingsController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get system settings
router.get('/', checkPermission('manage_system_settings'), getSystemSettings);

// Update system settings
router.patch('/', checkPermission('manage_system_settings'), updateSystemSettings);

// Toggle maintenance mode
router.post('/maintenance-mode', checkPermission('manage_system_settings'), toggleMaintenanceMode);

// Update allowed domains
router.post('/allowed-domains', checkPermission('manage_system_settings'), updateAllowedDomains);

// Toggle integration
router.post('/integrations', checkPermission('manage_system_settings'), toggleIntegration);

module.exports = router;
