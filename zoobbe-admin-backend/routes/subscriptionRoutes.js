const express = require('express');
const router = express.Router();
const { 
  getSubscriptions, 
  getSubscriptionById, 
  updateSubscriptionStatus, 
  applyDiscount,
  getSubscriptionPlans
} = require('../controllers/subscriptionController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get subscription plans
router.get('/plans', checkPermission('manage_subscriptions'), getSubscriptionPlans);

// Get all subscriptions
router.get('/', checkPermission('manage_subscriptions'), getSubscriptions);

// Get subscription by ID
router.get('/:subscriptionId', checkPermission('manage_subscriptions'), getSubscriptionById);

// Update subscription status
router.patch('/:subscriptionId/status', checkPermission('manage_subscriptions'), updateSubscriptionStatus);

// Apply discount to subscription
router.post('/:subscriptionId/discount', checkPermission('manage_subscriptions'), applyDiscount);

module.exports = router;
