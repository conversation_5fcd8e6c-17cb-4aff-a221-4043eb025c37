const express = require('express');
const router = express.Router();
const {
  getUsers,
  getUserById,
  updateUserStatus,
  getUserStats,
  impersonateUser,
  resetUserPassword,
  getUserLoginHistory,
  addUserNotes
} = require('../controllers/userController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get user statistics
router.get('/stats', checkPermission('view_stats'), getUserStats);

// Get all users
router.get('/', checkPermission('manage_users'), getUsers);

// Get user by ID
router.get('/:userId', checkPermission('manage_users'), getUserById);

// Update user status
router.patch('/:userId/status', checkPermission('manage_users'), updateUserStatus);

// Impersonate user
router.post('/:userId/impersonate', checkPermission('manage_users'), impersonate<PERSON>ser);

// Reset user password
router.post('/:userId/reset-password', checkPermission('manage_users'), resetUserPassword);

// Get user login history
router.get('/:userId/login-history', checkPermission('manage_users'), getUserLoginHistory);

// Add notes to user
router.post('/:userId/notes', checkPermission('manage_users'), addUserNotes);

module.exports = router;
