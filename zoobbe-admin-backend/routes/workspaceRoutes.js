const express = require('express');
const router = express.Router();
const { 
  getWorkspaces, 
  getWorkspaceById, 
  updateWorkspaceStatus, 
  getWorkspaceStats 
} = require('../controllers/workspaceController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get workspace statistics
router.get('/stats', checkPermission('view_stats'), getWorkspaceStats);

// Get all workspaces
router.get('/', checkPermission('manage_workspaces'), getWorkspaces);

// Get workspace by ID
router.get('/:workspaceId', checkPermission('manage_workspaces'), getWorkspaceById);

// Update workspace status
router.patch('/:workspaceId/status', checkPermission('manage_workspaces'), updateWorkspaceStatus);

module.exports = router;
