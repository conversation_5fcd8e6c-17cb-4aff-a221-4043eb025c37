require('dotenv').config();
const mongoose = require('mongoose');
const connectDB = require('../config/db');

// Function to check card data
const checkCardData = async () => {
  try {
    // Connect to database
    await connectDB();
    
    // Get a sample card from the database
    const card = await mongoose.connection.collection('cards').findOne({});
    
    if (!card) {
      console.log('No cards found in the database');
      return;
    }
    
    console.log('Sample card data:');
    console.log(JSON.stringify(card, null, 2));
    
    // Specifically check the board field
    console.log('\nBoard field type:', typeof card.board);
    console.log('Board field value:', card.board);
    
    // Close database connection
    await mongoose.connection.close();
    console.log('\nDatabase connection closed');
    
  } catch (error) {
    console.error('Error checking card data:', error);
  }
};

// Run the function
checkCardData();
