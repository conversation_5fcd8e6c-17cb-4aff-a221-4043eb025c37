require('dotenv').config();
const mongoose = require('mongoose');
const Admin = require('../models/Admin');
const connectDB = require('../config/db');
const bcrypt = require('bcryptjs');

// Default admin details
const DEFAULT_ADMIN = {
  name: 'Admin User',
  email: '<EMAIL>',
  password: 'adminPassword123',
  role: 'super-admin'
};

// Function to create admin user
const createAdmin = async () => {
  try {
    // Connect to database
    await connectDB();

    // Check if admin with this email already exists
    const existingAdmin = await Admin.findOne({ email: DEFAULT_ADMIN.email });

    if (existingAdmin) {
      console.log(`Admin with email ${DEFAULT_ADMIN.email} already exists.`);
      console.log('You can log in with:');
      console.log(`Email: ${DEFAULT_ADMIN.email}`);
      console.log(`Password: [your previously set password]`);
      process.exit(0);
    }

    // Create admin user - don't hash the password here, let the model's pre-save hook handle it
    const admin = await Admin.create({
      name: DEFAULT_ADMIN.name,
      email: DEFAULT_ADMIN.email,
      password: DEFAULT_ADMIN.password, // The model will hash this automatically
      role: DEFAULT_ADMIN.role,
      permissions: [
        'manage_users',
        'manage_workspaces',
        'manage_boards',
        'manage_cards',
        'manage_subscriptions',
        'manage_feature_flags',
        'manage_feedback',
        'manage_system_settings',
        'view_stats',
        'view_activity',
        'view_logs',
        'manage_admins'
      ]
    });

    console.log(`Admin user created successfully!`);
    console.log('You can now log in to the admin panel with these credentials:');
    console.log(`Email: ${DEFAULT_ADMIN.email}`);
    console.log(`Password: ${DEFAULT_ADMIN.password}`);

    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
};

// Run the function
createAdmin();
