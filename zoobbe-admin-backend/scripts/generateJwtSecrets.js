/**
 * <PERSON><PERSON><PERSON> to generate secure JWT secrets
 * Run with: node generateJwtSecrets.js
 */

const crypto = require('crypto');

// Generate a random string of 64 characters
function generateSecureSecret(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

const jwtSecret = generateSecureSecret();
const jwtRefreshSecret = generateSecureSecret();

console.log('Generated JWT secrets:');
console.log(`JWT_SECRET=${jwtSecret}`);
console.log(`JWT_REFRESH_SECRET=${jwtRefreshSecret}`);
console.log('\nCopy these values to your .env file to replace the default values.');
