require('dotenv').config();
const mongoose = require('mongoose');
const connectDB = require('../config/db');
const User = require('../models/User');
const Workspace = require('../models/Workspace');
const Board = require('../models/Board');
const Card = require('../models/Card');
const Activity = require('../models/Activity');
const Admin = require('../models/Admin');
const bcrypt = require('bcryptjs');

// Sample data
const users = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    username: 'joh<PERSON><PERSON>',
    passwordHash: bcrypt.hashSync('password123', 10),
    type: 'USER',
    verified: true,
    online: true,
    createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) // 60 days ago
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    username: 'jane<PERSON>',
    passwordHash: bcrypt.hashSync('password123', 10),
    type: 'USER',
    verified: true,
    online: false,
    createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000) // 45 days ago
  },
  {
    name: 'Admin User',
    email: '<EMAIL>',
    username: 'adminuser',
    passwordHash: bcrypt.hashSync('password123', 10),
    type: 'ADMIN',
    verified: true,
    online: true,
    createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) // 90 days ago
  },
  {
    name: 'New User',
    email: '<EMAIL>',
    username: 'newuser',
    passwordHash: bcrypt.hashSync('password123', 10),
    type: 'USER',
    verified: false,
    online: false,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
  }
];

// Function to seed data
const seedData = async () => {
  try {
    // Connect to database
    await connectDB();

    console.log('Connected to database');

    // Clear existing data
    await User.deleteMany({});
    await Workspace.deleteMany({});
    await Board.deleteMany({});
    await Card.deleteMany({});
    await Activity.deleteMany({});

    console.log('Cleared existing data');

    // Create users
    const createdUsers = await User.insertMany(users);
    console.log(`Created ${createdUsers.length} users`);

    // Function to generate a random shortId
    const generateShortId = () => {
      return Math.random().toString(36).substring(2, 10);
    };

    // Create workspaces
    const workspaces = [
      {
        name: 'Marketing Team',
        shortName: 'marketing',
        ownerId: createdUsers[0]._id,
        description: 'Marketing team workspace',
        shortId: generateShortId(),
        members: [
          { user: createdUsers[0]._id, role: 'admin' },
          { user: createdUsers[1]._id, role: 'member' }
        ],
        createdAt: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000) // 40 days ago
      },
      {
        name: 'Development Team',
        shortName: 'dev',
        ownerId: createdUsers[2]._id,
        description: 'Development team workspace',
        shortId: generateShortId(),
        members: [
          { user: createdUsers[2]._id, role: 'admin' },
          { user: createdUsers[0]._id, role: 'member' }
        ],
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
      },
      {
        name: 'Personal Workspace',
        shortName: 'personal',
        ownerId: createdUsers[1]._id,
        description: 'Personal workspace',
        shortId: generateShortId(),
        members: [
          { user: createdUsers[1]._id, role: 'admin' }
        ],
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000) // 20 days ago
      }
    ];

    const createdWorkspaces = await Workspace.insertMany(workspaces);
    console.log(`Created ${createdWorkspaces.length} workspaces`);

    // Create boards
    const boards = [
      {
        title: 'Marketing Campaign',
        workspace: createdWorkspaces[0]._id,
        shortId: generateShortId(),
        wShortId: createdWorkspaces[0].shortId,
        members: [
          { user: createdUsers[0]._id, role: 'admin' },
          { user: createdUsers[1]._id, role: 'member' }
        ],
        createdAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000) // 35 days ago
      },
      {
        title: 'Website Redesign',
        workspace: createdWorkspaces[1]._id,
        shortId: generateShortId(),
        wShortId: createdWorkspaces[1].shortId,
        members: [
          { user: createdUsers[2]._id, role: 'admin' },
          { user: createdUsers[0]._id, role: 'member' }
        ],
        createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000) // 25 days ago
      },
      {
        title: 'Personal Tasks',
        workspace: createdWorkspaces[2]._id,
        shortId: generateShortId(),
        wShortId: createdWorkspaces[2].shortId,
        members: [
          { user: createdUsers[1]._id, role: 'admin' }
        ],
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) // 15 days ago
      }
    ];

    const createdBoards = await Board.insertMany(boards);
    console.log(`Created ${createdBoards.length} boards`);

    // Update workspaces with boards
    for (let i = 0; i < createdWorkspaces.length; i++) {
      createdWorkspaces[i].boards.push(createdBoards[i]._id);
      await createdWorkspaces[i].save();
    }

    // Create activities
    const activities = [
      {
        user: createdUsers[0]._id,
        action: 'created',
        targetType: 'workspace',
        target: createdWorkspaces[0]._id,
        createdAt: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000) // 40 days ago
      },
      {
        user: createdUsers[2]._id,
        action: 'created',
        targetType: 'workspace',
        target: createdWorkspaces[1]._id,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
      },
      {
        user: createdUsers[1]._id,
        action: 'created',
        targetType: 'workspace',
        target: createdWorkspaces[2]._id,
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000) // 20 days ago
      },
      {
        user: createdUsers[0]._id,
        action: 'created',
        targetType: 'board',
        target: createdBoards[0]._id,
        createdAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000) // 35 days ago
      },
      {
        user: createdUsers[2]._id,
        action: 'created',
        targetType: 'board',
        target: createdBoards[1]._id,
        createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000) // 25 days ago
      },
      {
        user: createdUsers[1]._id,
        action: 'created',
        targetType: 'board',
        target: createdBoards[2]._id,
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) // 15 days ago
      },
      {
        user: createdUsers[0]._id,
        action: 'added',
        targetType: 'user',
        target: createdUsers[1]._id,
        metadata: { workspace: createdWorkspaces[0]._id },
        createdAt: new Date(Date.now() - 38 * 24 * 60 * 60 * 1000) // 38 days ago
      },
      {
        user: createdUsers[2]._id,
        action: 'added',
        targetType: 'user',
        target: createdUsers[0]._id,
        metadata: { workspace: createdWorkspaces[1]._id },
        createdAt: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000) // 28 days ago
      },
      {
        user: createdUsers[0]._id,
        action: 'login',
        targetType: 'system',
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // 1 day ago
      },
      {
        user: createdUsers[2]._id,
        action: 'login',
        targetType: 'system',
        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000) // 12 hours ago
      }
    ];

    const createdActivities = await Activity.insertMany(activities);
    console.log(`Created ${createdActivities.length} activities`);

    // Create admin user if it doesn't exist
    const adminExists = await Admin.findOne({ email: '<EMAIL>' });

    if (!adminExists) {
      await Admin.create({
        name: 'Super Admin',
        email: '<EMAIL>',
        password: 'adminPassword123',
        role: 'super-admin'
      });
      console.log('Created admin user');
    } else {
      console.log('Admin user already exists');
    }

    console.log('Data seeding completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding data:', error);
    process.exit(1);
  }
};

// Run the function
seedData();
