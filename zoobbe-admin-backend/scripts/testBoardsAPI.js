require('dotenv').config();
const axios = require('axios');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const Admin = require('../models/Admin');
const connectDB = require('../config/db');

// Generate JWT token for authentication
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: '1d'
  });
};

// Function to test boards API
const testBoardsAPI = async () => {
  try {
    // Connect to database to get an admin ID
    await connectDB();
    
    // Find an admin to use for authentication
    const admin = await Admin.findOne({ email: '<EMAIL>' });
    if (!admin) {
      console.error('Admin not found');
      process.exit(1);
    }
    
    // Generate token for authentication
    const token = generateToken(admin._id);
    
    // Get page and limit from command line arguments
    const page = process.argv[2] || 1;
    const limit = process.argv[3] || 50;
    
    console.log(`Testing boards API with page: ${page}, limit: ${limit}`);
    
    // Make API request
    const response = await axios.get(`http://localhost:5001/api/admin/boards?page=${page}&limit=${limit}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    // Log pagination info
    console.log('\nPagination Info:');
    console.log(JSON.stringify(response.data.pagination, null, 2));
    
    // Log number of boards returned
    console.log(`\nNumber of boards returned: ${response.data.boards.length}`);
    
    // Check if pagination is working correctly
    const { total, page: currentPage, limit: pageLimit, totalPages } = response.data.pagination;
    
    console.log(`\nTotal boards: ${total}`);
    console.log(`Current page: ${currentPage}`);
    console.log(`Page limit: ${pageLimit}`);
    console.log(`Total pages: ${totalPages}`);
    
    if (totalPages > 1 && currentPage < totalPages) {
      console.log(`\nThere are more pages available. You can fetch page ${parseInt(currentPage) + 1} next.`);
    } else if (totalPages > 1 && currentPage === totalPages) {
      console.log('\nYou are on the last page.');
    } else if (totalPages === 1) {
      console.log('\nThere is only one page of results.');
    }
    
    // Check if the total matches what's expected
    if (total === 333) {
      console.log('\nThe total count (333) matches the expected value ✅');
    } else {
      console.log(`\nThe total count (${total}) does not match the expected value (333) ❌`);
    }
    
  } catch (error) {
    console.error('Boards API Error:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response data:', error.response.data);
    } else {
      console.error(error.message);
    }
  } finally {
    // Disconnect from database
    await mongoose.connection.close();
  }
};

// Run the function
testBoardsAPI();
