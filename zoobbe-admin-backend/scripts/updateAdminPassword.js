require('dotenv').config();
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');
const Admin = require('../models/Admin');
const connectDB = require('../config/db');

// Function to update admin password
const updateAdminPassword = async (email, newPassword) => {
  try {
    await connectDB();

    console.log(`Updating password for admin with email: ${email}`);

    // Find the admin
    const admin = await Admin.findOne({ email });

    if (!admin) {
      console.log('Admin not found in database');
      return null;
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update the admin's password
    admin.password = hashedPassword;
    await admin.save();

    console.log('Admin password updated successfully');

    // Test the new password
    console.log('New password:', newPassword);
    console.log('Stored hash:', admin.password);
    const isMatch = await bcrypt.compare(newPassword, admin.password);
    console.log(`Password verification test: ${isMatch ? 'PASSED' : 'FAILED'}`);

    // Try a different approach to verify
    const manualHash = await bcrypt.hash(newPassword, 10);
    console.log('Manually generated hash:', manualHash);
    const manualCompare = await bcrypt.compare(newPassword, manualHash);
    console.log(`Manual verification test: ${manualCompare ? 'PASSED' : 'FAILED'}`);

    return admin;
  } catch (error) {
    console.error('Error updating admin password:', error.message);
    return null;
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
};

// Main function
const main = async () => {
  try {
    // Get email and new password from command line arguments
    const email = process.argv[2];
    const newPassword = process.argv[3];

    if (!email || !newPassword) {
      console.error('Please provide email and new password as arguments');
      console.log('Usage: node updateAdminPassword.js <email> <newPassword>');
      process.exit(1);
    }

    await updateAdminPassword(email, newPassword);
  } catch (error) {
    console.error('Error:', error.message);
  }
};

// Run the main function
main();
