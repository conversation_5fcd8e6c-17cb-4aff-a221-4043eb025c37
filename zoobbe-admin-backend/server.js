require('dotenv').config();
const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const bodyParser = require('body-parser');
const connectDB = require('./config/db');

// Import routes
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const workspaceRoutes = require('./routes/workspaceRoutes');
const statsRoutes = require('./routes/statsRoutes');
const activityRoutes = require('./routes/activityRoutes');
const boardRoutes = require('./routes/boardRoutes');
const cardRoutes = require('./routes/cardRoutes');
const subscriptionRoutes = require('./routes/subscriptionRoutes');
const featureFlagRoutes = require('./routes/featureFlagRoutes');
const feedbackRoutes = require('./routes/feedbackRoutes');
const settingsRoutes = require('./routes/settingsRoutes');

const app = express();
const PORT = process.env.PORT || 5001; // Use a different port than the main backend

// CORS configuration
const corsOptions = {
  origin: [
    process.env.ADMIN_FRONTEND_URL || 'http://localhost:3002',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:5173'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  optionsSuccessStatus: 200,
};

// Middleware
app.use(cookieParser());
app.use(cors(corsOptions));
app.options('*', cors(corsOptions));
app.use(express.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Connect to database
connectDB();

// Health check route
app.get('/healthcheck', (req, res) => {
  res.status(200).json({ status: 'OK' });
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/workspaces', workspaceRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/activity', activityRoutes);
app.use('/api/admin/boards', boardRoutes);
app.use('/api/admin/cards', cardRoutes);
app.use('/api/admin/subscriptions', subscriptionRoutes);
app.use('/api/admin/feature-flags', featureFlagRoutes);
app.use('/api/admin/feedback', feedbackRoutes);
app.use('/api/admin/settings', settingsRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Internal Server Error' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Admin server is running on port ${PORT}`);
});

module.exports = app;
