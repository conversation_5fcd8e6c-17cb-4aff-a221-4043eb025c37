<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tailwind CSS Custom Components Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        fontFamily: {
          'sans': ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
          'mono': ['"Intel One Mono"', 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
        },
        extend: {
          colors: {
            slate: {
              50: '#f8fafc',
              100: '#f1f5f9',
              200: '#e2e8f0',
              300: '#cbd5e1',
              400: '#94a3b8',
              500: '#64748b',
              600: '#475569',
              700: '#334155',
              800: '#1e293b',
              900: '#0f172a',
              950: '#020617',
            },
          }
        }
      }
    }
  </script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Intel One Mono - Regular */
    @font-face {
      font-family: 'Intel One Mono';
      font-style: normal;
      font-weight: 400;
      font-display: swap;
      src: url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff2/IntelOneMono-Regular.woff2') format('woff2'),
           url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff/IntelOneMono-Regular.woff') format('woff');
    }
    
    /* Custom component classes */
    .btn {
      @apply px-4 py-2 rounded-lg transition-colors duration-200;
    }
    
    .btn-primary {
      @apply bg-slate-800 text-white hover:bg-slate-700;
    }
    
    .btn-secondary {
      @apply bg-slate-100 text-slate-800 hover:bg-slate-200;
    }
    
    .card {
      @apply bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden;
    }
    
    .card-header {
      @apply p-4 border-b border-gray-100 bg-gray-50;
    }
    
    .card-body {
      @apply p-4;
    }
    
    .card-footer {
      @apply p-4 border-t border-gray-100 bg-gray-50;
    }
  </style>
</head>
<body class="bg-gray-100 p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold text-slate-800 mb-6">Tailwind CSS Custom Components Test</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <!-- Regular Tailwind Card -->
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <h2 class="text-xl font-semibold text-slate-700 mb-4">Regular Tailwind</h2>
        <p class="text-slate-600 mb-4">This card uses regular Tailwind classes directly.</p>
        <div class="flex space-x-2">
          <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">Button</button>
          <button type="button" class="bg-slate-200 hover:bg-slate-300 text-slate-700 px-4 py-2 rounded-lg transition-colors">Cancel</button>
        </div>
      </div>
      
      <!-- Custom Component Card -->
      <div class="card">
        <div class="card-header">
          <h2 class="text-xl font-semibold text-slate-700">Custom Components</h2>
        </div>
        <div class="card-body">
          <p class="text-slate-600 mb-4">This card uses our custom component classes.</p>
          <div class="flex space-x-2">
            <button type="button" class="btn btn-primary">Primary</button>
            <button type="button" class="btn btn-secondary">Secondary</button>
          </div>
        </div>
        <div class="card-footer">
          <p class="text-sm text-gray-500">Card Footer</p>
        </div>
      </div>
    </div>
    
    <!-- Font Test -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200 mb-8">
      <h2 class="text-xl font-semibold text-slate-700 mb-4">Font Test</h2>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <h3 class="text-lg font-medium text-slate-700 mb-2">Inter Font</h3>
          <p class="text-slate-600">The quick brown fox jumps over the lazy dog.</p>
          <p class="text-slate-600 font-bold">The quick brown fox jumps over the lazy dog.</p>
        </div>
        <div>
          <h3 class="text-lg font-medium text-slate-700 mb-2">Intel One Mono</h3>
          <p class="font-mono text-slate-600">The quick brown fox jumps over the lazy dog.</p>
          <p class="font-mono text-slate-600 font-bold">The quick brown fox jumps over the lazy dog.</p>
        </div>
      </div>
    </div>
    
    <!-- Code Block -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-200">
      <div class="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-gray-200">
        <span class="text-xs font-medium text-gray-500 uppercase">JavaScript</span>
        <button type="button" class="p-1.5 rounded-md text-gray-500 hover:bg-gray-200 transition-colors duration-200">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
        </button>
      </div>
      <div class="overflow-x-auto bg-gray-50">
        <pre class="p-4 text-sm">
          <code class="font-mono text-gray-800">// Example JavaScript code
const greeting = "Hello, World!";
console.log(greeting);

function add(a, b) {
  return a + b;
}

const result = add(5, 10);
console.log(`The result is: ${result}`);</code>
        </pre>
      </div>
    </div>
  </div>
</body>
</html>
