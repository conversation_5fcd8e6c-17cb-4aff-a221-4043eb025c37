/* InfiniteVirtualList Styles */
.infinite-virtual-list {
  width: 100%;
  border-radius: 4px;
  overflow: auto;
  position: relative;
  height: 100%;
}

/* Virtual list container */
.virtual-list-container {
  width: 100%;
  position: relative;
}

/* Virtual item */
.virtual-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

/* Load more trigger */
.load-more-trigger {
  height: 20px;
  width: 100%;
}

/* Loading indicator styles */
.loading-indicator {
  text-align: center;
  padding: 10px;
  font-style: italic;
  color: #666;
  background-color: #f9f9f9;
  border-top: 1px solid #e0e0e0;
}

/* Empty list message styles */
.empty-list {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f9f9f9;
}

/* Scrollbar styles for better UX */
.infinite-virtual-list::-webkit-scrollbar {
  width: 8px;
}

.infinite-virtual-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.infinite-virtual-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.infinite-virtual-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* For Firefox */
.infinite-virtual-list {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}
