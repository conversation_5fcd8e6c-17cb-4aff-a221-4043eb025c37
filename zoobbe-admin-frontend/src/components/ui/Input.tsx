import React, { InputHTMLAttributes, forwardRef } from 'react';

interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  helperText?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  variant?: 'default' | 'filled' | 'outline';
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      helperText,
      error,
      leftIcon,
      rightIcon,
      size = 'md',
      fullWidth = false,
      variant = 'default',
      className = '',
      disabled = false,
      ...props
    },
    ref
  ) => {
    const baseClasses = "block w-full rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0 shadow-sm";

    const variantClasses = {
      default: "border border-gray-300 focus:border-primary-500 focus:ring-primary-500 bg-white dark:bg-slate-800 dark:border-slate-700 dark:text-white dark:placeholder-gray-400",
      filled: "border border-transparent bg-gray-100 focus:bg-white focus:border-primary-500 focus:ring-primary-500 dark:bg-slate-700 dark:focus:bg-slate-800 dark:text-white dark:placeholder-gray-400",
      outline: "border border-gray-300 focus:border-primary-500 focus:ring-primary-500 bg-transparent dark:border-slate-700 dark:text-white dark:placeholder-gray-400"
    };

    const sizeClasses = {
      sm: "px-3 py-1.5 text-sm",
      md: "px-4 py-2 text-base",
      lg: "px-5 py-2.5 text-lg"
    };

    const errorClasses = error
      ? "border-rose-500 focus:border-rose-500 focus:ring-rose-500 text-rose-500 dark:text-rose-400 dark:border-rose-500"
      : "";

    const disabledClasses = disabled
      ? "opacity-60 cursor-not-allowed bg-gray-100 dark:bg-slate-700"
      : "";

    const widthClass = fullWidth ? "w-full" : "";

    const inputClasses = [
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      errorClasses,
      disabledClasses,
      widthClass,
      className
    ].filter(Boolean).join(' ');

    const iconSizeClasses = {
      sm: "h-4 w-4",
      md: "h-5 w-5",
      lg: "h-6 w-6"
    };

    return (
      <div className={`${fullWidth ? 'w-full' : ''}`}>
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className={`text-gray-500 dark:text-gray-400 ${iconSizeClasses[size]}`}>{leftIcon}</span>
            </div>
          )}
          <input
            ref={ref}
            className={`
              ${inputClasses}
              ${leftIcon ? 'pl-10' : ''}
              ${rightIcon ? 'pr-10' : ''}
            `}
            disabled={disabled}
            {...props}
          />
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span className={`text-gray-500 dark:text-gray-400 ${iconSizeClasses[size]}`}>{rightIcon}</span>
            </div>
          )}
        </div>
        {(helperText || error) && (
          <p className={`mt-1 text-sm ${error ? 'text-rose-500 dark:text-rose-400' : 'text-gray-500 dark:text-gray-400'}`}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
