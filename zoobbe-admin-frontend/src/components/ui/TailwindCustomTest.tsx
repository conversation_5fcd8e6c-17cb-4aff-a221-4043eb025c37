import React from 'react';

const TailwindCustomTest: React.FC = () => {
  return (
    <div className="p-8 max-w-md mx-auto">
      <h2 className="text-2xl font-bold text-slate-800 mb-4">Custom Tailwind Classes Test</h2>
      
      <div className="card mb-6">
        <div className="card-header">
          <h3 className="text-lg font-medium">Card Title</h3>
        </div>
        <div className="card-body">
          <p className="text-gray-600 mb-4">
            This card is using our custom Tailwind components defined in the tailwind.css file.
          </p>
          <div className="flex space-x-3">
            <button className="btn btn-primary">Primary</button>
            <button className="btn btn-secondary">Secondary</button>
          </div>
        </div>
        <div className="card-footer">
          <p className="text-sm text-gray-500">Card Footer</p>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-slate-100 p-4 rounded-lg">
          <p className="font-mono text-slate-700">Regular Tailwind</p>
        </div>
        <div className="card">
          <div className="card-body">
            <p className="font-mono text-slate-700">Custom Components</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TailwindCustomTest;
