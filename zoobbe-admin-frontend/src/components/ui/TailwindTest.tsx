import React from 'react';

const TailwindTest: React.FC = () => {
  return (
    <div className="p-8 max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl m-4">
      <div className="md:flex">
        <div className="md:shrink-0">
          <div className="h-48 w-full md:h-full md:w-48 bg-gradient-to-r from-slate-800 to-slate-700 flex items-center justify-center">
            <span className="text-4xl font-bold text-white font-mono">Z</span>
          </div>
        </div>
        <div className="p-8">
          <div className="uppercase tracking-wide text-sm text-indigo-500 font-semibold">Tailwind CSS Test</div>
          <h2 className="block mt-1 text-lg leading-tight font-medium text-black">
            Testing Tailwind CSS Integration
          </h2>
          <p className="mt-2 text-slate-500">
            This component is using Tailwind CSS classes to verify that the integration is working correctly.
          </p>
          <div className="mt-4 flex space-x-3">
            <button type="button" className="bg-slate-800 hover:bg-slate-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
              Primary
            </button>
            <button type="button" className="bg-slate-100 hover:bg-slate-200 text-slate-800 px-4 py-2 rounded-lg transition-colors duration-200">
              Secondary
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TailwindTest;
