import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI } from '../services/api';

interface Admin {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
}

interface AuthContextType {
  admin: Admin | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Check if user is authenticated on initial load
  useEffect(() => {
    const checkAuthentication = async () => {
      await checkAuth();
      setIsLoading(false);
    };

    checkAuthentication();
  }, []);

  const checkAuth = async (): Promise<boolean> => {
    try {
      // Try to get the profile - this will work if the HTTP-only cookie is valid
      const response = await authAPI.getProfile();
      setAdmin(response.data.admin);
      setIsAuthenticated(true);
      return true;
    } catch (error: any) {
      console.error('Authentication check failed:', error);

      // If the error is 401 (Unauthorized), try to refresh the token
      if (error.response?.status === 401) {
        try {
          // Try to refresh the token
          const refreshResponse = await authAPI.refreshToken();
          setAdmin(refreshResponse.data.admin);
          setIsAuthenticated(true);

          // If we get a token in the response, store it as a fallback
          if (refreshResponse.data.token) {
            localStorage.setItem('adminToken', refreshResponse.data.token);
          }

          return true;
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          localStorage.removeItem('adminToken');
          setIsAuthenticated(false);
          setAdmin(null);
          return false;
        }
      }

      // If not a 401 error or refresh failed
      localStorage.removeItem('adminToken');
      setIsAuthenticated(false);
      setAdmin(null);
      return false;
    }
  };

  const login = async (email: string, password: string): Promise<void> => {
    try {
      const response = await authAPI.login(email, password);
      const { admin, token } = response.data;

      // The token is already set in HTTP-only cookies by the backend
      // We'll still store it in localStorage as a fallback for the Authorization header
      if (token) {
        localStorage.setItem('adminToken', token);
      }

      // Set admin and authentication state
      setAdmin(admin);
      setIsAuthenticated(true);

      console.log('Login successful:', admin);
    } catch (error) {
      console.error('Login failed:', error);
      localStorage.removeItem('adminToken');
      setAdmin(null);
      setIsAuthenticated(false);
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      localStorage.removeItem('adminToken');
      setAdmin(null);
      setIsAuthenticated(false);
    }
  };

  const value = {
    admin,
    isAuthenticated,
    isLoading,
    login,
    logout,
    checkAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
