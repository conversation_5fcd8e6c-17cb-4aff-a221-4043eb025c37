import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  isDarkMode: boolean;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Apply theme immediately to prevent flickering
const applyTheme = (theme: Theme): boolean => {
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

  // Determine if dark mode should be active
  const shouldBeDark =
    theme === 'dark' ||
    (theme === 'system' && prefersDark);

  // Apply or remove dark class and set data-theme attribute
  if (shouldBeDark) {
    document.documentElement.classList.add('dark');
    document.documentElement.setAttribute('data-theme', 'dark');
  } else {
    document.documentElement.classList.remove('dark');
    document.documentElement.setAttribute('data-theme', 'light');
  }

  return shouldBeDark;
};

// Initialize theme from localStorage or default to system
const savedTheme = (localStorage.getItem('theme') as Theme) || 'system';
const initialIsDark = applyTheme(savedTheme);

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [theme, setThemeState] = useState<Theme>(savedTheme);
  const [isDarkMode, setIsDarkMode] = useState<boolean>(initialIsDark);

  // Handle theme changes
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem('theme', newTheme);
    setIsDarkMode(applyTheme(newTheme));
  };

  // Listen for system theme changes when in system mode
  useEffect(() => {
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      const handleChange = () => {
        setIsDarkMode(applyTheme('system'));
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, isDarkMode, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
