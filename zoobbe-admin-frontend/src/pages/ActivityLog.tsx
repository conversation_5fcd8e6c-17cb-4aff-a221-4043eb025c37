import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchActivityLog, fetchActivityTypes } from '../store/slices/activitySlice';

const ActivityLog = () => {
  const dispatch = useAppDispatch();
  const { data, types, loading, error, pagination } = useAppSelector((state) => state.activity);

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [type, setType] = useState('');
  const [search, setSearch] = useState('');

  useEffect(() => {
    dispatch(fetchActivityTypes());
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchActivityLog({ page, limit, type, search }));
  }, [dispatch, page, limit, type, search]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (loading && !data.length) {
    return <div id="activity-loading" className="loading page-loading">Loading activity log...</div>;
  }

  if (error) {
    return <div id="activity-error" className="error page-error">Error loading activity log: {error}</div>;
  }

  return (
    <div id="activity-page" className="activity-page page-container">
      <h1 className="page-title">Activity Log</h1>

      <div id="activity-filters" className="filters filter-section">
        <div id="activity-search" className="search-box filter-item">
          <input
            type="text"
            placeholder="Search activity..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="search-input"
          />
        </div>

        <div id="activity-type-filter" className="type-filter filter-item">
          <label htmlFor="activity-type-select" className="visually-hidden">Filter by activity type</label>
          <select
            id="activity-type-select"
            value={type}
            onChange={(e) => setType(e.target.value)}
            className="type-select filter-select"
            aria-label="Filter by activity type"
          >
            <option value="">All Activities</option>
            {types.map((activityType) => (
              <option key={activityType} value={activityType}>
                {activityType}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div id="activity-list" className="activity-list content-section">
        {data.map((activity) => (
          <div key={activity._id} id={`activity-item-${activity._id}`} className="activity-item list-item">
            <div className="activity-header item-header">
              <div className="activity-user user-info">
                {activity.user?.name || 'Unknown User'}
              </div>
              <div className="activity-time time-info">
                {formatDate(activity.createdAt)}
              </div>
            </div>

            <div className="activity-content item-content">
              <div className="activity-action action-info">
                <span className="action-type action-label">{activity.action}</span>
                <span className="target-type target-label">{activity.targetType}</span>
              </div>

              <div className="activity-details details-section">
                {activity.target && (
                  <div className="activity-target target-info">
                    Target: {activity.target}
                  </div>
                )}

                {activity.metadata && (
                  <div className="activity-metadata metadata-section">
                    <pre className="metadata-content">{JSON.stringify(activity.metadata, null, 2)}</pre>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div id="activity-pagination" className="pagination pagination-controls">
        <button
          id="prev-page-button"
          type="button"
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 1}
          className="pagination-button prev-button"
        >
          Previous
        </button>

        <span id="page-info" className="page-info pagination-info">
          Page {page} of {pagination.totalPages || 1}
        </span>

        <button
          id="next-page-button"
          type="button"
          onClick={() => handlePageChange(page + 1)}
          disabled={page >= (pagination.totalPages || 1)}
          className="pagination-button next-button"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default ActivityLog;
