import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { 
  fetchUserGrowthStats, 
  fetchWorkspaceGrowthStats,
  fetchActivityStats,
  fetchSubscriptionStats
} from '../store/slices/statsSlice';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

const Analytics = () => {
  const dispatch = useAppDispatch();
  const { 
    userGrowth, 
    workspaceGrowth, 
    activityStats,
    subscriptionStats,
    loading 
  } = useAppSelector((state) => state.stats);
  
  const [timeRange, setTimeRange] = useState('month');
  
  useEffect(() => {
    dispatch(fetchUserGrowthStats(timeRange));
    dispatch(fetchWorkspaceGrowthStats(timeRange));
    dispatch(fetchActivityStats());
    dispatch(fetchSubscriptionStats());
  }, [dispatch, timeRange]);
  
  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
  };
  
  if (loading && !userGrowth.length) {
    return <div className="loading">Loading analytics data...</div>;
  }
  
  return (
    <div className="analytics-page">
      <h1>Analytics</h1>
      
      <div className="time-range-selector">
        <button 
          className={`range-button ${timeRange === 'week' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('week')}
        >
          Last 7 Days
        </button>
        <button 
          className={`range-button ${timeRange === 'month' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('month')}
        >
          Last 30 Days
        </button>
        <button 
          className={`range-button ${timeRange === 'year' ? 'active' : ''}`}
          onClick={() => handleTimeRangeChange('year')}
        >
          Last 12 Months
        </button>
      </div>
      
      <div className="analytics-grid">
        <div className="chart-card">
          <h3>User Growth</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart
              data={userGrowth}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="count" 
                name="New Users" 
                stroke="#8884d8" 
                activeDot={{ r: 8 }} 
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        <div className="chart-card">
          <h3>Workspace Growth</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart
              data={workspaceGrowth}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="count" 
                name="New Workspaces" 
                stroke="#82ca9d" 
                activeDot={{ r: 8 }} 
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        <div className="chart-card">
          <h3>Activity by Type</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={activityStats?.byType || []}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="type" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="count" name="Activities" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        <div className="chart-card">
          <h3>Subscription Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={subscriptionStats?.byPlan || []}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={100}
                fill="#8884d8"
                dataKey="count"
                nameKey="plan"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {subscriptionStats?.byPlan?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
      
      <div className="stats-cards">
        <div className="stat-card">
          <h3>Total Users</h3>
          <div className="stat-value">{activityStats?.totalUsers || 0}</div>
          <div className="stat-change positive">
            +{activityStats?.newUsers || 0} in the last 30 days
          </div>
        </div>
        
        <div className="stat-card">
          <h3>Total Workspaces</h3>
          <div className="stat-value">{activityStats?.totalWorkspaces || 0}</div>
          <div className="stat-change positive">
            +{activityStats?.newWorkspaces || 0} in the last 30 days
          </div>
        </div>
        
        <div className="stat-card">
          <h3>Total Boards</h3>
          <div className="stat-value">{activityStats?.totalBoards || 0}</div>
        </div>
        
        <div className="stat-card">
          <h3>Total Cards</h3>
          <div className="stat-value">{activityStats?.totalCards || 0}</div>
        </div>
        
        <div className="stat-card">
          <h3>Active Subscriptions</h3>
          <div className="stat-value">{subscriptionStats?.totalActive || 0}</div>
          <div className="stat-change positive">
            ${subscriptionStats?.monthlyRevenue?.toFixed(2) || 0} monthly revenue
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
