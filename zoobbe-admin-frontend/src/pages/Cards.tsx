import { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchCards, updateCardStatus, resetCards } from '../store/slices/cardSlice';
import InfiniteVirtualList from '../components/common/InfiniteVirtualList';

const Cards = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { data, loading, error, hasNextPage } = useAppSelector((state) => state.cards);

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50); // Increased default limit to 50
  const [status, setStatus] = useState('');
  const [search, setSearch] = useState('');
  const [boardFilter, setBoardFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');

  // Reset cards when filters change
  useEffect(() => {
    dispatch(resetCards());
    setPage(1);
    loadCards(1);
  }, [status, search, boardFilter, priorityFilter, limit]);

  // Load cards based on current filters and pagination
  const loadCards = useCallback((pageToLoad: number) => {
    dispatch(fetchCards({
      page: pageToLoad,
      limit,
      status,
      search,
      board: boardFilter,
      priority: priorityFilter
    }));
  }, [dispatch, limit, status, search, boardFilter, priorityFilter]);

  // Load next page of cards for infinite scroll
  const loadNextPage = useCallback(() => {
    if (hasNextPage && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      loadCards(nextPage);
    }
  }, [hasNextPage, loading, page, loadCards]);

  const handleStatusChange = async (cardId: string, newStatus: string) => {
    if (window.confirm(`Are you sure you want to change this card's status to ${newStatus}?`)) {
      try {
        await dispatch(updateCardStatus({ cardId, status: newStatus }));
        // Reset and refresh the card list
        dispatch(resetCards());
        setPage(1);
        loadCards(1);
      } catch (error) {
        console.error('Failed to update card status:', error);
      }
    }
  };



  if (loading && !data.length) {
    return (
      <div id="cards-loading" className="flex items-center justify-center h-64 w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        <span className="ml-3 text-lg font-medium text-gray-700 dark:text-gray-300">Loading cards...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div id="cards-error" className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 p-4 rounded-md m-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error loading cards</h3>
            <div className="mt-2 text-sm text-red-700 dark:text-red-300">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div id="cards-page" className="w-full px-6 py-8 animate-fadeIn">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-white">Cards</h1>
        <p className="mt-2 text-gray-400">Manage and monitor cards across boards</p>
      </div>

      <div id="cards-filters" className="mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <div id="cards-search" className="flex-grow min-w-[200px] max-w-md">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search cards..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 bg-slate-900 border border-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-white placeholder-gray-500"
                aria-label="Search cards"
              />
            </div>
          </div>

          <div id="cards-status-filter" className="w-40">
            <select
              id="status-select"
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full px-4 py-2.5 bg-slate-900 border border-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-white"
              aria-label="Filter by status"
            >
              <option value="">All Cards</option>
              <option value="active">Active</option>
              <option value="archived">Archived</option>
            </select>
          </div>

          <div id="cards-priority-filter" className="w-40">
            <select
              id="priority-select"
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="w-full px-4 py-2.5 bg-slate-900 border border-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-white"
              aria-label="Filter by priority"
            >
              <option value="">All Priorities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>

          <div id="cards-board-filter" className="w-48">
            <input
              type="text"
              placeholder="Filter by board ID..."
              value={boardFilter}
              onChange={(e) => setBoardFilter(e.target.value)}
              className="w-full px-4 py-2.5 bg-slate-900 border border-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-white placeholder-gray-500"
              aria-label="Filter by board ID"
            />
          </div>
        </div>
      </div>

      <div className="border border-slate-800 rounded-md overflow-hidden">
        <div id="cards-table-header" className="md:flex bg-slate-900 border-b border-slate-800 py-3 font-medium text-gray-400 text-xs uppercase tracking-wider">
          <div className="flex-1 px-6">Title</div>
          <div className="flex-1 px-6">Board</div>
          <div className="w-28 px-4 text-center">Priority</div>
          <div className="w-32 px-4">Due Date</div>
          <div className="w-28 px-4 text-center">Status</div>
          <div className="w-32 px-4 text-center">Actions</div>
        </div>

        <div id="cards-list-container" className="h-[calc(100vh-350px)] overflow-hidden">
          <InfiniteVirtualList
            items={data}
            hasNextPage={hasNextPage}
            isLoading={loading}
            loadNextPage={loadNextPage}
            estimateSize={70} // Estimated row height
            loadingComponent={
              <div className="flex justify-center items-center py-4 bg-slate-900 border-t border-slate-800">
                <div className="animate-spin h-5 w-5 mr-3 border-t-2 border-b-2 border-indigo-500 rounded-full"></div>
                <span className="text-sm text-gray-400">Loading more cards...</span>
              </div>
            }
            emptyComponent={
              <div className="flex flex-col items-center justify-center py-12 bg-slate-900 text-gray-400">
                <svg className="h-12 w-12 text-gray-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="text-lg font-medium text-gray-300">
                  {search || status || boardFilter || priorityFilter
                    ? 'No cards match your filters'
                    : 'No cards found'}
                </p>
              </div>
            }
            renderItem={(card: any) => (
              <div className="flex items-center border-b border-slate-800 hover:bg-slate-800/70 transition-colors bg-slate-900 text-white" key={card._id}>
                <div className="flex-1 p-4 px-6 truncate font-medium">{card.title}</div>
                <div className="flex-1 p-4 px-6 truncate">
                  <button
                    type="button"
                    className="text-indigo-600 hover:text-indigo-800 hover:underline font-medium dark:text-indigo-400 dark:hover:text-indigo-300"
                    onClick={() => navigate(`/boards/${card.board._id}`)}
                  >
                    {card.board.title}
                  </button>
                </div>
                <div className="w-28 p-4 text-center">
                  <span className={`inline-flex px-2 py-1 rounded text-xs font-medium ${
                    card.priority === 'high'
                      ? 'bg-red-900/30 text-red-300'
                      : card.priority === 'medium'
                        ? 'bg-yellow-900/30 text-yellow-300'
                        : 'bg-green-900/30 text-green-300'
                  }`}>
                    {card.priority}
                  </span>
                </div>
                <div className="w-32 p-4 text-gray-400">
                  {card.dueDate ? new Date(card.dueDate).toLocaleDateString() : 'No due date'}
                </div>
                <div className="w-28 p-4 text-center">
                  <span className={`inline-flex px-2 py-1 rounded text-xs font-medium ${
                    card.archived
                      ? 'bg-gray-800 text-gray-300'
                      : 'bg-green-900/30 text-green-300'
                  }`}>
                    {card.archived ? 'Archived' : 'Active'}
                  </span>
                </div>
                <div className="w-32 p-4 flex justify-center">
                  {card.archived ? (
                    <button
                      type="button"
                      onClick={() => handleStatusChange(card._id, 'active')}
                      className="px-3 py-1 rounded bg-green-900/30 text-green-300 text-xs font-medium hover:bg-green-900/50 transition-colors focus:outline-none"
                    >
                      Unarchive
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={() => handleStatusChange(card._id, 'archived')}
                      className="px-3 py-1 rounded bg-red-900/30 text-red-300 text-xs font-medium hover:bg-red-900/50 transition-colors focus:outline-none"
                    >
                      Archive
                    </button>
                  )}
                </div>
              </div>
            )}
          />
        </div>
      </div>

      <div id="cards-pagination" className="mt-4 flex items-center justify-end">
        <div className="flex items-center space-x-2">
          <label htmlFor="limit-select" className="text-sm text-gray-400">Items per page:</label>
          <select
            id="limit-select"
            value={limit}
            onChange={(e) => {
              setLimit(Number(e.target.value));
            }}
            aria-label="Number of items to display per page"
            className="block w-16 pl-2 pr-6 py-1 text-sm border border-slate-800 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 bg-slate-900 text-gray-300 transition-colors"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="200">200</option>
          </select>
        </div>

        <div className="ml-4 text-sm text-gray-400">
          {data.length > 0 && (
            <span>
              Showing <span className="font-medium">{data.length}</span> of <span className="font-medium">{data.length + (hasNextPage ? '...' : '')}</span> cards
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default Cards;
