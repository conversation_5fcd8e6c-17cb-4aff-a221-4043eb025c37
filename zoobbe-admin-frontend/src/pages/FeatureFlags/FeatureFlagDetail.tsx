import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  RiArrowLeftLine,
  RiEditLine,
  RiDeleteBinLine,
  RiToggleLine,
  RiToggleFill
} from 'react-icons/ri';
import {
  Card,
  Button,
  Badge,
  Alert,
  Modal,
  Form,
  Input,
  Textarea,
  Select,
  Checkbox
} from '../../components/ui';
import { getFeatureFlagById, updateFeatureFlag, deleteFeatureFlag } from '../../services/api';

interface FeatureFlag {
  _id: string;
  name: string;
  description: string;
  enabled: boolean;
  rolloutPercentage: number;
  isExperimental: boolean;
  targetUsers: string[];
  targetWorkspaces: string[];
  targetPlans: string[];
  startDate: string | null;
  endDate: string | null;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    _id: string;
    name: string;
    email: string;
  };
  updatedBy: {
    _id: string;
    name: string;
    email: string;
  };
}

const FeatureFlagDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [flag, setFlag] = useState<FeatureFlag | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modals
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Edit form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    enabled: false,
    rolloutPercentage: 0,
    isExperimental: false
  });

  // Target tabs
  const [activeTab, setActiveTab] = useState('users');

  useEffect(() => {
    if (id) {
      fetchFeatureFlag();
    }
  }, [id]);

  const fetchFeatureFlag = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await getFeatureFlagById(id!);
      setFlag(response.featureFlag);

      // Initialize form data
      setFormData({
        name: response.featureFlag.name,
        description: response.featureFlag.description || '',
        enabled: response.featureFlag.enabled,
        rolloutPercentage: response.featureFlag.rolloutPercentage,
        isExperimental: response.featureFlag.isExperimental
      });
    } catch (err) {
      setError('Failed to fetch feature flag details. Please try again.');
      console.error('Error fetching feature flag:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    navigate('/feature-flags');
  };

  const handleToggleStatus = async () => {
    if (!flag) return;

    try {
      const updatedFlag = await updateFeatureFlag(flag._id, {
        enabled: !flag.enabled
      });

      setFlag(updatedFlag.featureFlag);
    } catch (err) {
      setError('Failed to update feature flag status. Please try again.');
      console.error('Error updating feature flag:', err);
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!flag) return;

    try {
      const updatedFlag = await updateFeatureFlag(flag._id, formData);
      setFlag(updatedFlag.featureFlag);
      setShowEditModal(false);
    } catch (err) {
      setError('Failed to update feature flag. Please try again.');
      console.error('Error updating feature flag:', err);
    }
  };

  const handleDeleteFlag = async () => {
    if (!flag) return;

    try {
      await deleteFeatureFlag(flag._id);
      navigate('/feature-flags', { state: { message: 'Feature flag deleted successfully' } });
    } catch (err) {
      setError('Failed to delete feature flag. Please try again.');
      console.error('Error deleting feature flag:', err);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox'
        ? (e.target as HTMLInputElement).checked
        : type === 'number'
          ? Number(value)
          : value
    }));
  };

  if (loading) {
    return (
      <div className="feature-flag-detail">
        <Card>
          <div className="loading-spinner"></div>
          <p className="text-center">Loading feature flag details...</p>
        </Card>
      </div>
    );
  }

  if (error || !flag) {
    return (
      <div className="feature-flag-detail">
        <Card>
          <Alert variant="error" title="Error">
            {error || 'Feature flag not found'}
          </Alert>
          <Button
            variant="outline"
            icon={<RiArrowLeftLine />}
            onClick={handleGoBack}
          >
            Back to Feature Flags
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="feature-flag-detail">
      <div className="flag-header">
        <div className="flag-header-left">
          <Button
            variant="outline"
            icon={<RiArrowLeftLine />}
            onClick={handleGoBack}
          >
            Back
          </Button>
          <h1>{flag.name}</h1>
          <Badge
            variant={flag.enabled ? 'success' : 'secondary'}
          >
            {flag.enabled ? 'Enabled' : 'Disabled'}
          </Badge>
          {flag.isExperimental && (
            <Badge variant="warning">Experimental</Badge>
          )}
        </div>
        <div className="flag-actions">
          <Button
            variant={flag.enabled ? 'success' : 'secondary'}
            leftIcon={flag.enabled ? <RiToggleFill /> : <RiToggleLine />}
            onClick={handleToggleStatus}
          >
            {flag.enabled ? 'Disable' : 'Enable'}
          </Button>
          <Button
            variant="primary"
            leftIcon={<RiEditLine />}
            onClick={() => setShowEditModal(true)}
          >
            Edit
          </Button>
          <Button
            variant="danger"
            leftIcon={<RiDeleteBinLine />}
            onClick={() => setShowDeleteModal(true)}
          >
            Delete
          </Button>
        </div>
      </div>

      <Card>
        <div className="flag-section">
          <h2>Flag Details</h2>
          <div className="flag-info">
            <div className="info-item">
              <div className="info-label">Description</div>
              <div className="info-value">
                {flag.description || <span className="text-muted">No description</span>}
              </div>
            </div>
            <div className="info-item">
              <div className="info-label">Created By</div>
              <div className="info-value">{flag.createdBy?.name || 'Unknown'}</div>
            </div>
            <div className="info-item">
              <div className="info-label">Last Updated By</div>
              <div className="info-value">{flag.updatedBy?.name || 'Unknown'}</div>
            </div>
            <div className="info-item">
              <div className="info-label">Created At</div>
              <div className="info-value">
                {new Date(flag.createdAt).toLocaleDateString()}
                {' '}
                {new Date(flag.createdAt).toLocaleTimeString()}
              </div>
            </div>
            <div className="info-item">
              <div className="info-label">Last Updated</div>
              <div className="info-value">
                {new Date(flag.updatedAt).toLocaleDateString()}
                {' '}
                {new Date(flag.updatedAt).toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>

        <div className="flag-section">
          <h2>Rollout Configuration</h2>
          <div className="rollout-slider">
            <div className="slider-header">
              <div className="slider-label">Rollout Percentage</div>
              <div className="slider-value">{flag.rolloutPercentage}%</div>
            </div>
            <div className="slider-container">
              <div
                className="slider-progress"
                style={{ width: `${flag.rolloutPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="flag-section target-section">
          <h2>Target Configuration</h2>
          <div className="target-tabs">
            <div
              className={`target-tab ${activeTab === 'users' ? 'active' : ''}`}
              onClick={() => setActiveTab('users')}
            >
              Users
            </div>
            <div
              className={`target-tab ${activeTab === 'workspaces' ? 'active' : ''}`}
              onClick={() => setActiveTab('workspaces')}
            >
              Workspaces
            </div>
            <div
              className={`target-tab ${activeTab === 'plans' ? 'active' : ''}`}
              onClick={() => setActiveTab('plans')}
            >
              Plans
            </div>
          </div>

          <div className="target-content">
            {activeTab === 'users' && (
              <div>
                {flag.targetUsers && flag.targetUsers.length > 0 ? (
                  <div>
                    {/* User list would go here */}
                    <p>{flag.targetUsers.length} users targeted</p>
                  </div>
                ) : (
                  <p className="text-muted">No specific users targeted</p>
                )}
              </div>
            )}

            {activeTab === 'workspaces' && (
              <div>
                {flag.targetWorkspaces && flag.targetWorkspaces.length > 0 ? (
                  <div>
                    {/* Workspace list would go here */}
                    <p>{flag.targetWorkspaces.length} workspaces targeted</p>
                  </div>
                ) : (
                  <p className="text-muted">No specific workspaces targeted</p>
                )}
              </div>
            )}

            {activeTab === 'plans' && (
              <div>
                {flag.targetPlans && flag.targetPlans.length > 0 ? (
                  <div className="plan-badges">
                    {flag.targetPlans.map(plan => (
                      <Badge key={plan} variant="primary">
                        {plan.charAt(0).toUpperCase() + plan.slice(1)}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted">No specific plans targeted</p>
                )}
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Feature Flag"
        size="medium"
        footer={
          <>
            <Button
              variant="outline"
              onClick={() => setShowEditModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleEditSubmit}
            >
              Save Changes
            </Button>
          </>
        }
      >
        <Form onSubmit={handleEditSubmit}>
          <Input
            label="Name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            required
            fullWidth
          />

          <Textarea
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            fullWidth
          />

          <div className="form-row">
            <Input
              label="Rollout Percentage"
              name="rolloutPercentage"
              type="number"
              min="0"
              max="100"
              value={formData.rolloutPercentage.toString()}
              onChange={handleInputChange}
            />
          </div>

          <Checkbox
            label="Experimental Feature"
            name="isExperimental"
            checked={formData.isExperimental}
            onChange={handleInputChange as any}
          />

          <Checkbox
            label="Enabled"
            name="enabled"
            checked={formData.enabled}
            onChange={handleInputChange as any}
          />
        </Form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Feature Flag"
        size="small"
        footer={
          <>
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteFlag}
            >
              Delete
            </Button>
          </>
        }
      >
        <p>Are you sure you want to delete the feature flag "{flag.name}"?</p>
        <p>This action cannot be undone.</p>
      </Modal>
    </div>
  );
};

export default FeatureFlagDetail;
