import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { RiAddLine, RiFilterLine, RiRefreshLine } from 'react-icons/ri';
import { 
  Card, 
  Button, 
  Table, 
  Badge, 
  Pagination,
  Select,
  FormGroup
} from '../../components/ui';
import { getFeatureFlags } from '../../services/api';

interface FeatureFlag {
  _id: string;
  name: string;
  description: string;
  enabled: boolean;
  rolloutPercentage: number;
  isExperimental: boolean;
  createdAt: string;
  updatedAt: string;
}

const FeatureFlagsList: React.FC = () => {
  const navigate = useNavigate();
  const [flags, setFlags] = useState<FeatureFlag[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // Filters
  const [enabledFilter, setEnabledFilter] = useState<string>('all');
  const [experimentalFilter, setExperimentalFilter] = useState<string>('all');
  
  useEffect(() => {
    fetchFeatureFlags();
  }, [currentPage, enabledFilter, experimentalFilter]);
  
  const fetchFeatureFlags = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Build query params
      const params = new URLSearchParams();
      params.append('page', currentPage.toString());
      
      if (enabledFilter !== 'all') {
        params.append('enabled', enabledFilter);
      }
      
      if (experimentalFilter !== 'all') {
        params.append('experimental', experimentalFilter);
      }
      
      const response = await getFeatureFlags(params.toString());
      setFlags(response.featureFlags);
      
      // Set total pages from pagination info if available
      if (response.pagination) {
        setTotalPages(response.pagination.totalPages);
      }
    } catch (err) {
      setError('Failed to fetch feature flags. Please try again.');
      console.error('Error fetching feature flags:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const handleViewFlag = (flag: FeatureFlag) => {
    navigate(`/feature-flags/${flag._id}`);
  };
  
  const handleCreateFlag = () => {
    navigate('/feature-flags/new');
  };
  
  const handleRefresh = () => {
    fetchFeatureFlags();
  };
  
  const columns = [
    {
      key: 'name',
      header: 'Name',
      width: '20%',
      render: (flag: FeatureFlag) => (
        <div className="flag-name">
          <span>{flag.name}</span>
          {flag.isExperimental && (
            <Badge variant="warning" size="small">Experimental</Badge>
          )}
        </div>
      )
    },
    {
      key: 'description',
      header: 'Description',
      width: '30%',
      render: (flag: FeatureFlag) => (
        <div className="flag-description">
          {flag.description || <span className="text-muted">No description</span>}
        </div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      width: '15%',
      render: (flag: FeatureFlag) => (
        <Badge 
          variant={flag.enabled ? 'success' : 'secondary'}
        >
          {flag.enabled ? 'Enabled' : 'Disabled'}
        </Badge>
      )
    },
    {
      key: 'rolloutPercentage',
      header: 'Rollout',
      width: '15%',
      render: (flag: FeatureFlag) => (
        <div className="flag-rollout">
          <div className="rollout-bar">
            <div 
              className="rollout-progress" 
              style={{ width: `${flag.rolloutPercentage}%` }}
            ></div>
          </div>
          <span>{flag.rolloutPercentage}%</span>
        </div>
      )
    },
    {
      key: 'updatedAt',
      header: 'Last Updated',
      width: '20%',
      render: (flag: FeatureFlag) => (
        <span>
          {new Date(flag.updatedAt).toLocaleDateString()} 
          {' '}
          {new Date(flag.updatedAt).toLocaleTimeString()}
        </span>
      )
    }
  ];
  
  return (
    <div className="feature-flags-list">
      <div className="page-header">
        <h1>Feature Flags</h1>
        <Button 
          variant="primary" 
          icon={<RiAddLine />}
          onClick={handleCreateFlag}
        >
          Create Flag
        </Button>
      </div>
      
      <Card>
        <div className="filters">
          <FormGroup>
            <Select
              label="Status"
              options={[
                { value: 'all', label: 'All' },
                { value: 'true', label: 'Enabled' },
                { value: 'false', label: 'Disabled' }
              ]}
              value={enabledFilter}
              onChange={setEnabledFilter}
            />
            
            <Select
              label="Type"
              options={[
                { value: 'all', label: 'All' },
                { value: 'true', label: 'Experimental' },
                { value: 'false', label: 'Stable' }
              ]}
              value={experimentalFilter}
              onChange={setExperimentalFilter}
            />
          </FormGroup>
          
          <Button 
            variant="outline" 
            size="small"
            icon={<RiRefreshLine />}
            onClick={handleRefresh}
          >
            Refresh
          </Button>
        </div>
        
        <Table
          data={flags}
          columns={columns}
          loading={loading}
          emptyMessage={error || "No feature flags found"}
          onRowClick={handleViewFlag}
        />
        
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      </Card>
    </div>
  );
};

export default FeatureFlagsList;
