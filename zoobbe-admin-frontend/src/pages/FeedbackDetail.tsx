import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  fetchFeedbackById,
  updateFeedbackStatus,
  addFeedbackResponse,
  addInternalNote,
  resetCurrentFeedback
} from '../store/slices/feedbackSlice';

const FeedbackDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { currentFeedback, loading, error } = useAppSelector((state) => state.feedback);
  const { admin } = useAppSelector((state) => state.auth);

  const [responseMessage, setResponseMessage] = useState('');
  const [internalNote, setInternalNote] = useState('');

  useEffect(() => {
    if (id) {
      dispatch(fetchFeedbackById(id));
    }

    return () => {
      dispatch(resetCurrentFeedback());
    };
  }, [dispatch, id]);

  const handleStatusChange = async (newStatus: string) => {
    if (!id) return;

    if (window.confirm(`Are you sure you want to change this feedback's status to ${newStatus}?`)) {
      try {
        await dispatch(updateFeedbackStatus({ feedbackId: id, status: newStatus }));
      } catch (error) {
        console.error('Failed to update feedback status:', error);
      }
    }
  };

  const handleSendResponse = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id || !responseMessage.trim()) return;

    try {
      await dispatch(addFeedbackResponse({ feedbackId: id, message: responseMessage }));
      setResponseMessage('');
    } catch (error) {
      console.error('Failed to send response:', error);
    }
  };

  const handleAddNote = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id || !internalNote.trim()) return;

    try {
      await dispatch(addInternalNote({ feedbackId: id, note: internalNote }));
      setInternalNote('');
    } catch (error) {
      console.error('Failed to add note:', error);
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center p-8 text-gray-600">Loading feedback details...</div>;
  }

  if (error) {
    return <div className="p-8 text-red-600 bg-red-50 rounded-md">Error loading feedback details: {error}</div>;
  }

  if (!currentFeedback) {
    return <div className="p-8 text-gray-600 bg-gray-50 rounded-md">Feedback not found</div>;
  }

  return (
    <div className="p-5 max-w-7xl mx-auto">
      <div className="flex items-center mb-5">
        <button
          type="button"
          className="bg-transparent border-none text-blue-500 cursor-pointer text-base mr-5 p-0"
          onClick={() => navigate('/feedback')}
        >
          ← Back to Feedback
        </button>
        <h1 className="text-2xl font-bold">{currentFeedback.title}</h1>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-8 bg-gray-100 p-4 rounded-lg">
        <div className="flex flex-col">
          <span className="font-bold mb-1 text-gray-600">Status:</span>
          <span className={`inline-block px-2 py-1 rounded text-sm font-medium capitalize ${
            currentFeedback.status === 'new' ? 'bg-blue-100 text-blue-900' :
            currentFeedback.status === 'in_progress' ? 'bg-amber-100 text-amber-900' :
            currentFeedback.status === 'resolved' ? 'bg-green-100 text-green-900' :
            'bg-gray-200 text-gray-700'
          }`}>
            {currentFeedback.status.replace('_', ' ').charAt(0).toUpperCase() + currentFeedback.status.replace('_', ' ').slice(1)}
          </span>
        </div>
        <div className="flex flex-col">
          <span className="font-bold mb-1 text-gray-600">Type:</span>
          <span className={`inline-block px-2 py-1 rounded text-sm font-medium capitalize ${
            currentFeedback.type === 'bug' ? 'bg-red-100 text-red-900' :
            currentFeedback.type === 'feature' ? 'bg-indigo-100 text-indigo-900' :
            currentFeedback.type === 'improvement' ? 'bg-cyan-100 text-cyan-900' :
            currentFeedback.type === 'question' ? 'bg-purple-100 text-purple-900' :
            'bg-gray-100 text-gray-700'
          }`}>
            {currentFeedback.type.charAt(0).toUpperCase() + currentFeedback.type.slice(1)}
          </span>
        </div>
        <div className="flex flex-col">
          <span className="font-bold mb-1 text-gray-600">Priority:</span>
          <span className={`inline-block px-2 py-1 rounded text-sm font-medium capitalize ${
            currentFeedback.priority === 'low' ? 'bg-green-100 text-green-900' :
            currentFeedback.priority === 'medium' ? 'bg-amber-100 text-amber-900' :
            currentFeedback.priority === 'high' ? 'bg-orange-100 text-orange-900' :
            currentFeedback.priority === 'critical' ? 'bg-red-100 text-red-900' :
            'bg-gray-100 text-gray-700'
          }`}>
            {currentFeedback.priority.charAt(0).toUpperCase() + currentFeedback.priority.slice(1)}
          </span>
        </div>
        <div className="flex flex-col">
          <span className="font-bold mb-1 text-gray-600">Submitted by:</span>
          <span>{currentFeedback.user?.name || 'Unknown'} ({currentFeedback.user?.email})</span>
        </div>
        <div className="flex flex-col">
          <span className="font-bold mb-1 text-gray-600">Submitted on:</span>
          <span>{new Date(currentFeedback.createdAt).toLocaleString()}</span>
        </div>
      </div>

      <div className="flex gap-2.5 mb-8">
        {currentFeedback.status === 'new' && (
          <button
            type="button"
            onClick={() => handleStatusChange('in_progress')}
            className="px-4 py-2 bg-amber-100 text-amber-900 rounded font-medium hover:bg-amber-200 transition-colors"
          >
            Start Progress
          </button>
        )}

        {currentFeedback.status === 'in_progress' && (
          <button
            type="button"
            onClick={() => handleStatusChange('resolved')}
            className="px-4 py-2 bg-green-100 text-green-900 rounded font-medium hover:bg-green-200 transition-colors"
          >
            Mark as Resolved
          </button>
        )}

        {currentFeedback.status === 'resolved' && (
          <button
            type="button"
            onClick={() => handleStatusChange('closed')}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded font-medium hover:bg-gray-300 transition-colors"
          >
            Close Feedback
          </button>
        )}

        {(currentFeedback.status === 'resolved' || currentFeedback.status === 'closed') && (
          <button
            type="button"
            onClick={() => handleStatusChange('in_progress')}
            className="px-4 py-2 bg-purple-100 text-purple-900 rounded font-medium hover:bg-purple-200 transition-colors"
          >
            Reopen
          </button>
        )}
      </div>

      <div className="mb-8 bg-white p-5 rounded-lg shadow-sm">
        <h2 className="mt-0 mb-4 text-xl font-semibold text-gray-800">Description</h2>
        <div className="space-y-2.5">
          {currentFeedback.description.split('\n').map((line, i) => (
            <p key={i} className="leading-relaxed">{line}</p>
          ))}
        </div>
      </div>

      <div className="mb-8 bg-white p-5 rounded-lg shadow-sm">
        <h2 className="mt-0 mb-4 text-xl font-semibold text-gray-800">Responses</h2>
        {currentFeedback?.responses?.length === 0 ? (
          <p className="text-gray-500 italic">No responses yet.</p>
        ) : (
          <div className="space-y-4 mb-5">
            {currentFeedback?.responses?.map((response) => (
              <div key={response._id} className="p-4 rounded-lg bg-gray-50">
                <div className="flex justify-between mb-2.5 text-sm">
                  <span className="font-bold">{response.createdBy}</span>
                  <span className="text-gray-500">{new Date(response.createdAt).toLocaleString()}</span>
                </div>
                <div className="space-y-2.5">
                  {response.message.split('\n').map((line, i) => (
                    <p key={i} className="leading-relaxed">{line}</p>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        <form className="bg-gray-50 p-4 rounded-lg" onSubmit={handleSendResponse}>
          <h3 className="mt-0 mb-2.5 text-lg font-medium">Add Response</h3>
          <textarea
            value={responseMessage}
            onChange={(e) => setResponseMessage(e.target.value)}
            placeholder="Type your response here..."
            rows={4}
            className="w-full p-2.5 border border-gray-300 rounded resize-y mb-2.5"
            required
          />
          <button
            type="submit"
            className="px-4 py-2 bg-blue-500 text-white rounded font-medium hover:bg-blue-600 transition-colors"
          >
            Send Response
          </button>
        </form>
      </div>

      <div className="mb-8 bg-white p-5 rounded-lg shadow-sm">
        <h2 className="mt-0 mb-4 text-xl font-semibold text-gray-800">Internal Notes</h2>
        {currentFeedback?.notes?.length === 0 ? (
          <p className="text-gray-500 italic">No internal notes yet.</p>
        ) : (
          <div className="space-y-4 mb-5">
            {currentFeedback?.notes?.map((note) => (
              <div key={note._id} className="p-4 rounded-lg bg-gray-50">
                <div className="flex justify-between mb-2.5 text-sm">
                  <span className="font-bold">{note.createdBy}</span>
                  <span className="text-gray-500">{new Date(note.createdAt).toLocaleString()}</span>
                </div>
                <div className="space-y-2.5">
                  {note.note.split('\n').map((line, i) => (
                    <p key={i} className="leading-relaxed">{line}</p>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        <form className="bg-gray-50 p-4 rounded-lg" onSubmit={handleAddNote}>
          <h3 className="mt-0 mb-2.5 text-lg font-medium">Add Internal Note</h3>
          <textarea
            value={internalNote}
            onChange={(e) => setInternalNote(e.target.value)}
            placeholder="Type your internal note here..."
            rows={4}
            className="w-full p-2.5 border border-gray-300 rounded resize-y mb-2.5"
            required
          />
          <button
            type="submit"
            className="px-4 py-2 bg-blue-500 text-white rounded font-medium hover:bg-blue-600 transition-colors"
          >
            Add Note
          </button>
        </form>
      </div>
    </div>
  );
};

export default FeedbackDetail;
