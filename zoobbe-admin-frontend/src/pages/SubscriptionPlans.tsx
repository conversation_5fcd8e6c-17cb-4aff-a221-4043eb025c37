import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchSubscriptionPlans, updateSubscriptionPlan } from '../store/slices/subscriptionSlice';

const SubscriptionPlans = () => {
  const dispatch = useAppDispatch();
  const { plans, loading, error } = useAppSelector((state) => state.subscriptions);
  
  const [editingPlanId, setEditingPlanId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState({
    name: '',
    description: '',
    price: 0,
    interval: 'month',
    features: '',
    isActive: true
  });
  
  useEffect(() => {
    dispatch(fetchSubscriptionPlans());
  }, [dispatch]);
  
  const handleEditClick = (plan: any) => {
    setEditingPlanId(plan._id);
    setEditForm({
      name: plan.name,
      description: plan.description,
      price: plan.price,
      interval: plan.interval,
      features: plan.features.join('\n'),
      isActive: plan.isActive
    });
  };
  
  const handleCancelEdit = () => {
    setEditingPlanId(null);
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const target = e.target as HTMLInputElement;
      setEditForm({
        ...editForm,
        [name]: target.checked
      });
    } else {
      setEditForm({
        ...editForm,
        [name]: value
      });
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingPlanId) return;
    
    try {
      const updatedPlan = {
        ...editForm,
        price: Number(editForm.price),
        features: editForm.features.split('\n').filter(f => f.trim() !== '')
      };
      
      await dispatch(updateSubscriptionPlan({ 
        planId: editingPlanId, 
        planData: updatedPlan 
      }));
      
      setEditingPlanId(null);
      dispatch(fetchSubscriptionPlans());
    } catch (error) {
      console.error('Failed to update subscription plan:', error);
    }
  };
  
  if (loading && !plans.length) {
    return <div className="loading">Loading subscription plans...</div>;
  }
  
  if (error) {
    return <div className="error">Error loading subscription plans: {error}</div>;
  }
  
  return (
    <div className="subscription-plans-page">
      <h1>Subscription Plans</h1>
      
      <div className="plans-grid">
        {plans.map((plan) => (
          <div key={plan._id} className={`plan-card ${!plan.isActive ? 'inactive' : ''}`}>
            {editingPlanId === plan._id ? (
              <form onSubmit={handleSubmit} className="edit-plan-form">
                <h3>Edit Plan</h3>
                
                <div className="form-group">
                  <label htmlFor="name">Plan Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={editForm.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="description">Description</label>
                  <textarea
                    id="description"
                    name="description"
                    value={editForm.description}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="price">Price</label>
                    <input
                      type="number"
                      id="price"
                      name="price"
                      value={editForm.price}
                      onChange={handleInputChange}
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="interval">Interval</label>
                    <select
                      id="interval"
                      name="interval"
                      value={editForm.interval}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="month">Monthly</option>
                      <option value="year">Yearly</option>
                    </select>
                  </div>
                </div>
                
                <div className="form-group">
                  <label htmlFor="features">Features (one per line)</label>
                  <textarea
                    id="features"
                    name="features"
                    value={editForm.features}
                    onChange={handleInputChange}
                    rows={5}
                    required
                  />
                </div>
                
                <div className="form-group checkbox">
                  <label>
                    <input
                      type="checkbox"
                      name="isActive"
                      checked={editForm.isActive}
                      onChange={(e) => setEditForm({
                        ...editForm,
                        isActive: e.target.checked
                      })}
                    />
                    Active
                  </label>
                </div>
                
                <div className="form-actions">
                  <button type="submit" className="save">Save Changes</button>
                  <button type="button" className="cancel" onClick={handleCancelEdit}>Cancel</button>
                </div>
              </form>
            ) : (
              <>
                <div className="plan-header">
                  <h3>{plan.name}</h3>
                  {!plan.isActive && <span className="inactive-badge">Inactive</span>}
                </div>
                
                <div className="plan-price">
                  ${plan.price} / {plan.interval === 'month' ? 'month' : 'year'}
                </div>
                
                <p className="plan-description">{plan.description}</p>
                
                <div className="plan-features">
                  <h4>Features</h4>
                  <ul>
                    {plan.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
                
                <div className="plan-actions">
                  <button 
                    className="edit"
                    onClick={() => handleEditClick(plan)}
                  >
                    Edit Plan
                  </button>
                </div>
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SubscriptionPlans;
