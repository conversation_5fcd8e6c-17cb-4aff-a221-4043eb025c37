import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchSubscriptions, updateSubscriptionStatus } from '../store/slices/subscriptionSlice';

const Subscriptions = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { data, loading, error, pagination } = useAppSelector((state) => state.subscriptions);
  
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [status, setStatus] = useState('');
  const [search, setSearch] = useState('');
  const [planFilter, setPlanFilter] = useState('');
  
  useEffect(() => {
    dispatch(fetchSubscriptions({ 
      page, 
      limit, 
      status, 
      search,
      plan: planFilter
    }));
  }, [dispatch, page, limit, status, search, planFilter]);
  
  const handleStatusChange = async (subscriptionId: string, newStatus: string) => {
    if (window.confirm(`Are you sure you want to change this subscription's status to ${newStatus}?`)) {
      try {
        await dispatch(updateSubscriptionStatus({ subscriptionId, status: newStatus }));
        // Refresh the subscription list
        dispatch(fetchSubscriptions({ 
          page, 
          limit, 
          status, 
          search,
          plan: planFilter
        }));
      } catch (error) {
        console.error('Failed to update subscription status:', error);
      }
    }
  };
  
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };
  
  if (loading && !data.length) {
    return <div className="loading">Loading subscriptions...</div>;
  }
  
  if (error) {
    return <div className="error">Error loading subscriptions: {error}</div>;
  }
  
  return (
    <div className="subscriptions-page">
      <h1>Subscriptions</h1>
      
      <div className="filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="Search by user or workspace..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        
        <div className="status-filter">
          <select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="canceled">Canceled</option>
            <option value="past_due">Past Due</option>
            <option value="trialing">Trialing</option>
          </select>
        </div>
        
        <div className="plan-filter">
          <select
            value={planFilter}
            onChange={(e) => setPlanFilter(e.target.value)}
          >
            <option value="">All Plans</option>
            <option value="basic">Basic</option>
            <option value="pro">Pro</option>
            <option value="business">Business</option>
          </select>
        </div>
      </div>
      
      <div className="subscriptions-table">
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>User/Workspace</th>
              <th>Plan</th>
              <th>Status</th>
              <th>Start Date</th>
              <th>End Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.map((subscription) => (
              <tr key={subscription._id}>
                <td>{subscription._id}</td>
                <td>
                  {subscription.user ? (
                    <button
                      className="link-button"
                      onClick={() => navigate(`/users/${subscription.user._id}`)}
                    >
                      {subscription.user.name} (User)
                    </button>
                  ) : subscription.workspace ? (
                    <button
                      className="link-button"
                      onClick={() => navigate(`/workspaces/${subscription.workspace._id}`)}
                    >
                      {subscription.workspace.name} (Workspace)
                    </button>
                  ) : 'Unknown'}
                </td>
                <td>{subscription.plan}</td>
                <td>
                  <span className={`status ${subscription.status}`}>
                    {subscription.status}
                  </span>
                </td>
                <td>{new Date(subscription.startDate).toLocaleDateString()}</td>
                <td>
                  {subscription.endDate ? 
                    new Date(subscription.endDate).toLocaleDateString() : 
                    'No end date'}
                </td>
                <td>
                  <div className="actions">
                    {subscription.status === 'active' ? (
                      <button
                        onClick={() => handleStatusChange(subscription._id, 'canceled')}
                        className="cancel"
                      >
                        Cancel
                      </button>
                    ) : subscription.status === 'canceled' ? (
                      <button
                        onClick={() => handleStatusChange(subscription._id, 'active')}
                        className="activate"
                      >
                        Reactivate
                      </button>
                    ) : null}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="pagination">
        <button
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 1}
        >
          Previous
        </button>
        
        <span className="page-info">
          Page {page} of {pagination.totalPages || 1}
        </span>
        
        <button
          onClick={() => handlePageChange(page + 1)}
          disabled={page >= (pagination.totalPages || 1)}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default Subscriptions;
