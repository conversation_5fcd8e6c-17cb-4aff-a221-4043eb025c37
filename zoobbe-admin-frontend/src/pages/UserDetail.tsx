import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchUserById, updateUserStatus } from '../store/slices/userSlice';

const UserDetail = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { selectedUser, loading, error } = useAppSelector((state) => state.users);
  
  const [activeTab, setActiveTab] = useState('profile');
  
  useEffect(() => {
    if (userId) {
      dispatch(fetchUserById(userId));
    }
  }, [dispatch, userId]);
  
  const handleStatusChange = async (status: string) => {
    if (userId && window.confirm(`Are you sure you want to change this user's status to ${status}?`)) {
      try {
        await dispatch(updateUserStatus({ userId, status }));
        // Refresh user data
        dispatch(fetchUserById(userId));
      } catch (error) {
        console.error('Failed to update user status:', error);
      }
    }
  };
  
  if (loading && !selectedUser) {
    return <div className="loading">Loading user details...</div>;
  }
  
  if (error) {
    return <div className="error">Error loading user: {error}</div>;
  }
  
  if (!selectedUser) {
    return <div className="error">User not found</div>;
  }
  
  return (
    <div className="user-detail-page">
      <div className="page-header">
        <button 
          className="back-button"
          onClick={() => navigate('/users')}
        >
          ← Back to Users
        </button>
        <h1>User Details</h1>
      </div>
      
      <div className="user-profile-header">
        <div className="user-avatar">
          {selectedUser.profilePicture ? (
            <img src={selectedUser.profilePicture} alt={selectedUser.name} />
          ) : (
            <div className="avatar-placeholder">
              {selectedUser.name?.charAt(0) || 'U'}
            </div>
          )}
        </div>
        
        <div className="user-info">
          <h2>{selectedUser.name}</h2>
          <p className="user-email">{selectedUser.email}</p>
          <p className="user-username">@{selectedUser.username}</p>
          
          <div className="user-status">
            <span className={`status ${selectedUser.online ? 'active' : 'inactive'}`}>
              {selectedUser.online ? 'Online' : 'Offline'}
            </span>
            {!selectedUser.verified && (
              <span className="status unverified">Unverified</span>
            )}
            {selectedUser.suspended && (
              <span className="status suspended">Suspended</span>
            )}
            <span className={`status ${selectedUser.type === 'ADMIN' ? 'admin' : 'user'}`}>
              {selectedUser.type}
            </span>
          </div>
        </div>
        
        <div className="user-actions">
          <button
            onClick={() => handleStatusChange('verify')}
            disabled={selectedUser.verified}
            className={selectedUser.verified ? 'disabled' : ''}
          >
            Verify User
          </button>
          
          {selectedUser.type === 'ADMIN' ? (
            <button
              onClick={() => handleStatusChange('user')}
              className="warning"
            >
              Remove Admin
            </button>
          ) : (
            <button
              onClick={() => handleStatusChange('admin')}
              className="primary"
            >
              Make Admin
            </button>
          )}
          
          {selectedUser.suspended ? (
            <button
              onClick={() => handleStatusChange('active')}
              className="success"
            >
              Activate User
            </button>
          ) : (
            <button
              onClick={() => handleStatusChange('suspended')}
              className="danger"
            >
              Suspend User
            </button>
          )}
        </div>
      </div>
      
      <div className="tabs">
        <button 
          className={`tab ${activeTab === 'profile' ? 'active' : ''}`}
          onClick={() => setActiveTab('profile')}
        >
          Profile
        </button>
        <button 
          className={`tab ${activeTab === 'workspaces' ? 'active' : ''}`}
          onClick={() => setActiveTab('workspaces')}
        >
          Workspaces
        </button>
        <button 
          className={`tab ${activeTab === 'activity' ? 'active' : ''}`}
          onClick={() => setActiveTab('activity')}
        >
          Activity
        </button>
        <button 
          className={`tab ${activeTab === 'subscription' ? 'active' : ''}`}
          onClick={() => setActiveTab('subscription')}
        >
          Subscription
        </button>
      </div>
      
      <div className="tab-content">
        {activeTab === 'profile' && (
          <div className="profile-tab">
            <div className="detail-card">
              <h3>User Information</h3>
              <div className="detail-row">
                <span className="detail-label">ID:</span>
                <span className="detail-value">{selectedUser._id}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Name:</span>
                <span className="detail-value">{selectedUser.name}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Email:</span>
                <span className="detail-value">{selectedUser.email}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Username:</span>
                <span className="detail-value">{selectedUser.username}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Type:</span>
                <span className="detail-value">{selectedUser.type}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Verified:</span>
                <span className="detail-value">{selectedUser.verified ? 'Yes' : 'No'}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Created At:</span>
                <span className="detail-value">
                  {new Date(selectedUser.createdAt).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'workspaces' && (
          <div className="workspaces-tab">
            <h3>User Workspaces</h3>
            {selectedUser.workspaces && selectedUser.workspaces.length > 0 ? (
              <div className="workspaces-list">
                {selectedUser.workspaces.map((workspace) => (
                  <div key={workspace._id} className="workspace-item">
                    <h4>{workspace.name}</h4>
                    <p>{workspace.shortName}</p>
                    <button 
                      onClick={() => navigate(`/workspaces/${workspace._id}`)}
                      className="view-button"
                    >
                      View Workspace
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="no-data">No workspaces found for this user.</p>
            )}
          </div>
        )}
        
        {activeTab === 'activity' && (
          <div className="activity-tab">
            <h3>User Activity</h3>
            <p className="no-data">Activity log will be implemented in the next phase.</p>
          </div>
        )}
        
        {activeTab === 'subscription' && (
          <div className="subscription-tab">
            <h3>Subscription Details</h3>
            <div className="detail-card">
              <div className="detail-row">
                <span className="detail-label">Premium Member:</span>
                <span className="detail-value">
                  {selectedUser.isPremiumMember ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="subscription-actions">
                <button className="primary">
                  {selectedUser.isPremiumMember ? 'Manage Subscription' : 'Add Subscription'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserDetail;
