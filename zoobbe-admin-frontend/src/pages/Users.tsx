import { useEffect, useState, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchUsers, updateUserStatus } from '../store/slices/userSlice';
import InfiniteVirtualList from '../components/common/InfiniteVirtualList';

const Users = () => {
  const dispatch = useAppDispatch();
  const { data, loading, error, pagination } = useAppSelector((state) => state.users);

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50); // Increased default limit for better UX
  const [status, setStatus] = useState('');
  const [search, setSearch] = useState('');

  // Load users based on current filters and pagination
  const loadUsers = useCallback((pageToLoad: number) => {
    dispatch(fetchUsers({
      page: pageToLoad,
      limit,
      status,
      search
    }));
  }, [dispatch, limit, status, search]);

  // Reset and load first page when filters change
  useEffect(() => {
    setPage(1);
    loadUsers(1);
  }, [status, search, limit, loadUsers]);

  // Load next page of users for infinite scroll
  const loadNextPage = useCallback(() => {
    if (page < pagination.totalPages && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      loadUsers(nextPage);
    }
  }, [page, pagination.totalPages, loading, loadUsers]);

  const handleStatusChange = async (userId: string, newStatus: string) => {
    if (window.confirm(`Are you sure you want to change this user's status to ${newStatus}?`)) {
      try {
        await dispatch(updateUserStatus({ userId, status: newStatus }));
        // Refresh the user list
        setPage(1);
        loadUsers(1);
      } catch (error) {
        console.error('Failed to update user status:', error);
      }
    }
  };

  if (loading && !data.length) {
    return (
      <div id="users-loading" className="flex items-center justify-center h-64 w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        <span className="ml-3 text-lg font-medium text-gray-300">Loading users...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div id="users-error" className="bg-red-900/20 border-l-4 border-red-500 p-4 rounded-md m-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-300">Error loading users</h3>
            <div className="mt-2 text-sm text-red-400">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div id="users-page" className="w-full px-6 py-8 animate-fadeIn">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-white">Users</h1>
        <p className="mt-2 text-gray-400">Manage and monitor user accounts</p>
      </div>

      <div id="users-filters" className="flex flex-wrap gap-4 mb-6 items-center">
        <div id="users-search" className="flex-grow max-w-md">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search users..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="block w-full pl-10 pr-3 py-2.5 bg-slate-900 border border-slate-800 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 text-white placeholder-gray-500 transition-colors"
              aria-label="Search users"
            />
          </div>
        </div>

        <div id="users-status-filter">
          <label htmlFor="status-select" className="sr-only">Filter by status</label>
          <select
            id="status-select"
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            aria-label="Filter users by status"
            className="block w-full pl-3 pr-10 py-2.5 bg-slate-900 border border-slate-800 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 text-white transition-colors"
          >
            <option value="">All Users</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="unverified">Unverified</option>
          </select>
        </div>
      </div>

      <div className="border border-slate-800 rounded-md overflow-hidden">
        <div id="users-table-header" className="flex bg-slate-900 border-b border-slate-800 py-3 font-medium text-gray-400 text-xs uppercase tracking-wider">
          <div className="flex-1 px-6">Name</div>
          <div className="flex-1 px-6">Email</div>
          <div className="flex-1 px-6">Username</div>
          <div className="flex-1 px-6">Status</div>
          <div className="flex-1 px-6">Type</div>
          <div className="flex-1 px-6 text-right">Actions</div>
        </div>

        <div id="users-list-container" className="h-[calc(100vh-350px)] overflow-hidden">
          <InfiniteVirtualList
            items={data}
            hasNextPage={page < pagination.totalPages}
            isLoading={loading}
            loadNextPage={loadNextPage}
            estimateSize={70} // Estimated row height
            loadingComponent={
              <div id="users-loading-more" className="flex justify-center items-center py-4 bg-slate-900 border-t border-slate-800">
                <div className="animate-spin h-5 w-5 mr-3 border-t-2 border-b-2 border-indigo-500 rounded-full"></div>
                <span className="text-sm text-gray-400">Loading more users...</span>
              </div>
            }
            emptyComponent={
              <div id="users-empty-list" className="flex flex-col items-center justify-center py-12 bg-slate-900 text-gray-400">
                <svg className="h-12 w-12 text-gray-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <p className="text-lg font-medium text-gray-300">
                  {search || status ? 'No users match your filters' : 'No users found'}
                </p>
              </div>
            }
            renderItem={(user) => (
              <div className="flex border-b border-slate-800 hover:bg-slate-800/70 transition-colors bg-slate-900 text-white" key={user._id} id={`user-row-${user._id}`}>
                <div className="flex-1 px-6 py-4 truncate">{user.name}</div>
                <div className="flex-1 px-6 py-4 truncate">{user.email}</div>
                <div className="flex-1 px-6 py-4 truncate">{user.username}</div>
                <div className="flex-1 px-6 py-4">
                  <div className="flex flex-col space-y-1">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${user.online
                      ? 'bg-green-900/30 text-green-300'
                      : 'bg-gray-800 text-gray-300'
                      }`}>
                      {user.online ? 'Online' : 'Offline'}
                    </span>
                    {!user.verified && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-yellow-900/30 text-yellow-300">
                        Unverified
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex-1 px-6 py-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${user.type === 'ADMIN'
                    ? 'bg-purple-900/30 text-purple-300'
                    : 'bg-blue-900/30 text-blue-300'
                    }`}>
                    {user.type}
                  </span>
                </div>
                <div className="flex-1 px-6 py-4 text-right">
                  <div className="flex justify-end space-x-2">
                    <button
                      type="button"
                      onClick={() => handleStatusChange(user._id, 'verify')}
                      disabled={user.verified}
                      className={`px-3 py-1 text-xs font-medium rounded ${user.verified
                        ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                        : 'bg-indigo-900/30 text-indigo-300 hover:bg-indigo-900/50'
                        } transition-colors`}
                    >
                      {user.verified ? 'Verified' : 'Verify'}
                    </button>

                    {user.type === 'ADMIN' ? (
                      <button
                        type="button"
                        onClick={() => handleStatusChange(user._id, 'user')}
                        className="px-3 py-1 text-xs font-medium rounded bg-purple-900/30 text-purple-300 hover:bg-purple-900/50 transition-colors"
                      >
                        Remove Admin
                      </button>
                    ) : (
                      <button
                        type="button"
                        onClick={() => handleStatusChange(user._id, 'admin')}
                        className="px-3 py-1 text-xs font-medium rounded bg-purple-900/30 text-purple-300 hover:bg-purple-900/50 transition-colors"
                      >
                        Make Admin
                      </button>
                    )}

                    {user.suspended ? (
                      <button
                        type="button"
                        onClick={() => handleStatusChange(user._id, 'active')}
                        className="px-3 py-1 text-xs font-medium rounded bg-green-900/30 text-green-300 hover:bg-green-900/50 transition-colors"
                      >
                        Activate
                      </button>
                    ) : (
                      <button
                        type="button"
                        onClick={() => handleStatusChange(user._id, 'suspended')}
                        className="px-3 py-1 text-xs font-medium rounded bg-red-900/30 text-red-300 hover:bg-red-900/50 transition-colors"
                      >
                        Suspend
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}
          />
        </div>

        <div id="users-pagination" className="mt-6 flex items-center justify-end">
          <div className="flex items-center space-x-2">
            <label htmlFor="limit-select" className="text-sm text-gray-400">Items per page:</label>
            <select
              id="limit-select"
              value={limit}
              onChange={(e) => {
                setLimit(Number(e.target.value));
              }}
              aria-label="Number of items to display per page"
              className="block w-20 pl-3 pr-10 py-2 text-sm border border-slate-800 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 bg-slate-900 text-gray-300 transition-colors"
            >
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
              <option value="200">200</option>
            </select>
          </div>

          <div className="ml-4 text-sm text-gray-400">
            {data.length > 0 && (
              <span>
                Showing <span className="font-medium">{data.length}</span> of <span className="font-medium">{pagination.totalItems || 0}</span> users
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Users;
