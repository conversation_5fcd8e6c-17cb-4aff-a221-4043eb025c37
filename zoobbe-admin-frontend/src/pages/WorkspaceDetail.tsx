import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchWorkspaceById, updateWorkspaceStatus } from '../store/slices/workspaceSlice';

const WorkspaceDetail = () => {
  const { workspaceId } = useParams<{ workspaceId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { selectedWorkspace, loading, error } = useAppSelector((state) => state.workspaces);
  
  const [activeTab, setActiveTab] = useState('details');
  
  useEffect(() => {
    if (workspaceId) {
      dispatch(fetchWorkspaceById(workspaceId));
    }
  }, [dispatch, workspaceId]);
  
  const handleStatusChange = async (status: string) => {
    if (workspaceId && window.confirm(`Are you sure you want to change this workspace's status to ${status}?`)) {
      try {
        await dispatch(updateWorkspaceStatus({ workspaceId, status }));
        // Refresh workspace data
        dispatch(fetchWorkspaceById(workspaceId));
      } catch (error) {
        console.error('Failed to update workspace status:', error);
      }
    }
  };
  
  if (loading && !selectedWorkspace) {
    return <div className="loading">Loading workspace details...</div>;
  }
  
  if (error) {
    return <div className="error">Error loading workspace: {error}</div>;
  }
  
  if (!selectedWorkspace) {
    return <div className="error">Workspace not found</div>;
  }
  
  return (
    <div className="workspace-detail-page">
      <div className="page-header">
        <button 
          className="back-button"
          onClick={() => navigate('/workspaces')}
        >
          ← Back to Workspaces
        </button>
        <h1>Workspace Details</h1>
      </div>
      
      <div className="workspace-header">
        <div className="workspace-info">
          <h2>{selectedWorkspace.name}</h2>
          <p className="workspace-shortname">@{selectedWorkspace.shortName}</p>
          
          <div className="workspace-status">
            <span className={`status ${selectedWorkspace.status || 'active'}`}>
              {selectedWorkspace.status || 'Active'}
            </span>
          </div>
        </div>
        
        <div className="workspace-actions">
          {selectedWorkspace.status === 'suspended' ? (
            <button
              onClick={() => handleStatusChange('active')}
              className="success"
            >
              Activate Workspace
            </button>
          ) : (
            <button
              onClick={() => handleStatusChange('suspended')}
              className="danger"
            >
              Suspend Workspace
            </button>
          )}
        </div>
      </div>
      
      <div className="tabs">
        <button 
          className={`tab ${activeTab === 'details' ? 'active' : ''}`}
          onClick={() => setActiveTab('details')}
        >
          Details
        </button>
        <button 
          className={`tab ${activeTab === 'members' ? 'active' : ''}`}
          onClick={() => setActiveTab('members')}
        >
          Members
        </button>
        <button 
          className={`tab ${activeTab === 'boards' ? 'active' : ''}`}
          onClick={() => setActiveTab('boards')}
        >
          Boards
        </button>
        <button 
          className={`tab ${activeTab === 'subscription' ? 'active' : ''}`}
          onClick={() => setActiveTab('subscription')}
        >
          Subscription
        </button>
      </div>
      
      <div className="tab-content">
        {activeTab === 'details' && (
          <div className="details-tab">
            <div className="detail-card">
              <h3>Workspace Information</h3>
              <div className="detail-row">
                <span className="detail-label">ID:</span>
                <span className="detail-value">{selectedWorkspace._id}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Name:</span>
                <span className="detail-value">{selectedWorkspace.name}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Short Name:</span>
                <span className="detail-value">{selectedWorkspace.shortName}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Description:</span>
                <span className="detail-value">{selectedWorkspace.description || 'No description'}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Status:</span>
                <span className="detail-value">{selectedWorkspace.status || 'Active'}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Created At:</span>
                <span className="detail-value">
                  {new Date(selectedWorkspace.createdAt).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'members' && (
          <div className="members-tab">
            <h3>Workspace Members</h3>
            {selectedWorkspace.members && selectedWorkspace.members.length > 0 ? (
              <div className="members-table">
                <table>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Role</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedWorkspace.members.map((member) => (
                      <tr key={member.user._id}>
                        <td>{member.user.name}</td>
                        <td>{member.user.email}</td>
                        <td>
                          <span className={`role ${member.role}`}>
                            {member.role}
                          </span>
                        </td>
                        <td>
                          <button 
                            onClick={() => navigate(`/users/${member.user._id}`)}
                            className="view-button"
                          >
                            View User
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="no-data">No members found for this workspace.</p>
            )}
          </div>
        )}
        
        {activeTab === 'boards' && (
          <div className="boards-tab">
            <h3>Workspace Boards</h3>
            {selectedWorkspace.boards && selectedWorkspace.boards.length > 0 ? (
              <div className="boards-list">
                {selectedWorkspace.boards.map((board) => (
                  <div key={board._id} className="board-item">
                    <h4>{board.title}</h4>
                    <button 
                      onClick={() => navigate(`/boards/${board._id}`)}
                      className="view-button"
                    >
                      View Board
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="no-data">No boards found for this workspace.</p>
            )}
          </div>
        )}
        
        {activeTab === 'subscription' && (
          <div className="subscription-tab">
            <h3>Subscription Details</h3>
            <div className="detail-card">
              {selectedWorkspace.subscription ? (
                <>
                  <div className="detail-row">
                    <span className="detail-label">Plan:</span>
                    <span className="detail-value">
                      {selectedWorkspace.subscription.plan || 'Free'}
                    </span>
                  </div>
                  <div className="detail-row">
                    <span className="detail-label">Status:</span>
                    <span className="detail-value">
                      {selectedWorkspace.subscription.status || 'Active'}
                    </span>
                  </div>
                  <div className="subscription-actions">
                    <button className="primary">Manage Subscription</button>
                  </div>
                </>
              ) : (
                <>
                  <p>No subscription found for this workspace.</p>
                  <div className="subscription-actions">
                    <button className="primary">Add Subscription</button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkspaceDetail;
