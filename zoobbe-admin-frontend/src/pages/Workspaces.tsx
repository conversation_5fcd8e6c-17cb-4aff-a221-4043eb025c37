import { useEffect, useState, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchWorkspaces, updateWorkspaceStatus } from '../store/slices/workspaceSlice';
import InfiniteVirtualList from '../components/common/InfiniteVirtualList';

const Workspaces = () => {
  const dispatch = useAppDispatch();
  const { data, loading, error, pagination } = useAppSelector((state) => state.workspaces);

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50); // Increased default limit for better UX
  const [status, setStatus] = useState('');
  const [search, setSearch] = useState('');

  // Load workspaces based on current filters and pagination
  const loadWorkspaces = useCallback((pageToLoad: number) => {
    dispatch(fetchWorkspaces({
      page: pageToLoad,
      limit,
      status,
      search
    }));
  }, [dispatch, limit, status, search]);

  // Reset and load first page when filters change
  useEffect(() => {
    setPage(1);
    loadWorkspaces(1);
  }, [status, search, limit, loadWorkspaces]);

  // Load next page of workspaces for infinite scroll
  const loadNextPage = useCallback(() => {
    if (page < pagination.totalPages && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      loadWorkspaces(nextPage);
    }
  }, [page, pagination.totalPages, loading, loadWorkspaces]);

  const handleStatusChange = async (workspaceId: string, newStatus: string) => {
    if (window.confirm(`Are you sure you want to change this workspace's status to ${newStatus}?`)) {
      try {
        await dispatch(updateWorkspaceStatus({ workspaceId, status: newStatus }));
        // Refresh the workspace list
        setPage(1);
        loadWorkspaces(1);
      } catch (error) {
        console.error('Failed to update workspace status:', error);
      }
    }
  };

  if (loading && !data.length) {
    return (
      <div id="workspaces-loading" className="flex items-center justify-center h-64 w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        <span className="ml-3 text-lg font-medium text-gray-700 dark:text-gray-300">Loading workspaces...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div id="workspaces-error" className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 p-4 rounded-md m-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error loading workspaces</h3>
            <div className="mt-2 text-sm text-red-700 dark:text-red-300">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div id="workspaces-page" className="w-full px-6 py-8 animate-fadeIn">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-white">Workspaces</h1>
        <p className="mt-2 text-gray-400">Manage and monitor workspace environments</p>
      </div>

      <div id="workspaces-filters" className="mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <div id="workspaces-search" className="flex-grow min-w-[200px] max-w-md">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <input
                id="search-input"
                type="text"
                placeholder="Search workspaces..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 bg-slate-900 border border-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-white placeholder-gray-500"
                aria-label="Search workspaces"
              />
            </div>
          </div>

          <div id="workspaces-status-filter" className="w-56">
            <select
              id="status-select"
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              aria-label="Filter workspaces by status"
              className="w-full px-4 py-2.5 bg-slate-900 border border-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-white"
            >
              <option value="">All Workspaces</option>
              <option value="active">Active</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>
        </div>
      </div>

      <div className="border border-slate-800 rounded-md overflow-hidden">
        <div id="workspaces-table-header" className="md:flex bg-slate-900 border-b border-slate-800 py-3 font-medium text-gray-400 text-xs uppercase tracking-wider">
          <div className="flex-1 px-6">Name</div>
          <div className="flex-1 px-6">Short Name</div>
          <div className="flex-1 px-6">Owner</div>
          <div className="w-24 px-4 text-center">Members</div>
          <div className="w-28 px-4 text-center">Status</div>
          <div className="w-32 px-4 text-center">Actions</div>
        </div>

        <div id="workspaces-list-container" className="h-[calc(100vh-350px)] overflow-hidden">
          <InfiniteVirtualList
            items={data}
            hasNextPage={page < pagination.totalPages}
            isLoading={loading}
            loadNextPage={loadNextPage}
            estimateSize={70} // Estimated row height
            loadingComponent={
              <div id="workspaces-loading-more" className="text-center py-4 text-gray-500 bg-gray-50 border-t border-gray-200 dark:text-gray-400 dark:bg-slate-800 dark:border-slate-700">
                <div className="inline-flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Loading more workspaces...
                </div>
              </div>
            }
            emptyComponent={
              <div id="workspaces-empty-list" className="flex flex-col items-center justify-center p-12 text-center text-gray-500 bg-white dark:bg-slate-800 dark:text-gray-400">
                <svg className="w-16 h-16 mb-4 text-gray-300 dark:text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <p className="text-xl font-medium mb-2">
                  {search || status
                    ? 'No workspaces match your filters'
                    : 'No workspaces found'}
                </p>
                <p className="text-sm max-w-md">
                  {search || status
                    ? 'Try adjusting your search or filter criteria to find what you\'re looking for.'
                    : 'Create your first workspace to get started.'}
                </p>
              </div>
            }
            renderItem={(workspace) => (
              <div className="flex items-center border-b border-slate-800 hover:bg-slate-800/70 transition-colors bg-slate-900 text-white" key={workspace._id} id={`workspace-row-${workspace._id}`}>
                <div className="flex-1 p-4 px-6 truncate font-medium">{workspace.name}</div>
                <div className="flex-1 p-4 px-6 truncate font-mono text-sm text-gray-400">{workspace.shortName}</div>
                <div className="flex-1 p-4 px-6 truncate">
                  {workspace?.members?.find(m => m.role === 'admin')?.user?.name || 'Unknown'}
                </div>
                <div className="w-24 p-4 text-center">{workspace.members.length}</div>
                <div className="w-28 p-4 text-center">
                  <span className={`inline-flex px-2 py-1 rounded text-xs font-medium ${
                    workspace.status === 'suspended'
                      ? 'bg-red-900/30 text-red-300'
                      : 'bg-green-900/30 text-green-300'
                  }`}>
                    {workspace.status || 'Active'}
                  </span>
                </div>
                <div className="w-32 p-4 flex justify-center">
                  {workspace.status === 'suspended' ? (
                    <button
                      type="button"
                      onClick={() => handleStatusChange(workspace._id, 'active')}
                      className="px-3 py-1 rounded bg-green-900/30 text-green-300 text-xs font-medium hover:bg-green-900/50 transition-colors focus:outline-none"
                    >
                      Activate
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={() => handleStatusChange(workspace._id, 'suspended')}
                      className="px-3 py-1 rounded bg-red-900/30 text-red-300 text-xs font-medium hover:bg-red-900/50 transition-colors focus:outline-none"
                    >
                      Suspend
                    </button>
                  )}
                </div>
              </div>
            )}
          />
      </div>

      </div>

      <div id="workspaces-pagination" className="mt-4 flex items-center justify-end">
        <div className="flex items-center space-x-2">
          <label htmlFor="limit-select" className="text-sm text-gray-400">Items per page:</label>
          <select
            id="limit-select"
            value={limit}
            onChange={(e) => {
              setLimit(Number(e.target.value));
            }}
            aria-label="Number of items to display per page"
            className="block w-16 pl-2 pr-6 py-1 text-sm border border-slate-800 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 bg-slate-900 text-gray-300 transition-colors"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="200">200</option>
          </select>
        </div>

        <div className="ml-4 text-sm text-gray-400">
          {data.length > 0 && (
            <span>
              Showing <span className="font-medium">{data.length}</span> workspaces
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default Workspaces;
