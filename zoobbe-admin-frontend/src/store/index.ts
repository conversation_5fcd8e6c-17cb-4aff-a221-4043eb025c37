import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import userReducer from './slices/userSlice';
import workspaceReducer from './slices/workspaceSlice';
import boardReducer from './slices/boardSlice';
import cardReducer from './slices/cardSlice';
import subscriptionReducer from './slices/subscriptionSlice';
import settingsReducer from './slices/settingsSlice';
import statsReducer from './slices/statsSlice';
import activityReducer from './slices/activitySlice';
import feedbackReducer from './slices/feedbackSlice';

// Create the store
export const store = configureStore({
  reducer: {
    auth: authReducer,
    users: userReducer,
    workspaces: workspaceReducer,
    boards: boardReducer,
    cards: cardReducer,
    subscriptions: subscriptionReducer,
    settings: settingsReducer,
    stats: statsReducer,
    activity: activityReducer,
    feedback: feedbackReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type AppStore = typeof store;
