import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

interface Board {
  _id: string;
  title: string;
  workspace: {
    _id: string;
    name: string;
    shortId?: string;
  };
  members: Array<{
    user: {
      _id: string;
      name: string;
      email: string;
      username?: string;
    };
    role: string;
    status: string;
  }>;
  visibility: string;
  createdAt: string;
  updatedAt: string;
  archived: boolean;
  cards?: any[];
  cardsCount?: number;
  shortId?: string;
  labels?: Array<{
    text: string;
    color: string;
  }>;
}

interface BoardState {
  data: Board[];
  selectedBoard: Board | null;
  loading: boolean;
  error: string | null;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  hasNextPage: boolean;
}

export const fetchBoards = createAsyncThunk(
  'boards/fetchBoards',
  async (params: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
    workspace?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await api.get('/admin/boards', { params });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch boards');
    }
  }
);

export const fetchBoardById = createAsyncThunk(
  'boards/fetchBoardById',
  async (boardId: string, { rejectWithValue }) => {
    try {
      const response = await api.get(`/admin/boards/${boardId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch board');
    }
  }
);

export const updateBoardStatus = createAsyncThunk(
  'boards/updateBoardStatus',
  async ({ boardId, status }: { boardId: string; status: string }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/admin/boards/${boardId}/status`, { status });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update board status');
    }
  }
);

export const updateBoardDetailsThunk = createAsyncThunk(
  'boards/updateBoardDetails',
  async ({ boardId, data }: { boardId: string; data: { title?: string; visibility?: string } }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/admin/boards/${boardId}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update board details');
    }
  }
);

export const transferBoardThunk = createAsyncThunk(
  'boards/transferBoard',
  async ({ boardId, workspaceId }: { boardId: string; workspaceId: string }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/admin/boards/${boardId}/transfer`, { workspaceId });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to transfer board');
    }
  }
);

export const fetchBoardMembersThunk = createAsyncThunk(
  'boards/fetchBoardMembers',
  async (boardId: string, { rejectWithValue }) => {
    try {
      const response = await api.get(`/admin/boards/${boardId}/members`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch board members');
    }
  }
);

export const updateBoardMemberRoleThunk = createAsyncThunk(
  'boards/updateBoardMemberRole',
  async ({ boardId, userId, role }: { boardId: string; userId: string; role: string }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/admin/boards/${boardId}/members/${userId}`, { role });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update member role');
    }
  }
);

export const removeBoardMemberThunk = createAsyncThunk(
  'boards/removeBoardMember',
  async ({ boardId, userId }: { boardId: string; userId: string }, { rejectWithValue }) => {
    try {
      const response = await api.delete(`/admin/boards/${boardId}/members/${userId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to remove board member');
    }
  }
);

const initialState: BoardState = {
  data: [],
  selectedBoard: null,
  loading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 50,
    totalPages: 1,
  },
  hasNextPage: true,
};

const boardSlice = createSlice({
  name: 'boards',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSelectedBoard: (state) => {
      state.selectedBoard = null;
    },
    resetBoards: (state) => {
      state.data = [];
      state.pagination.page = 1;
      state.hasNextPage = true;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Boards
      .addCase(fetchBoards.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBoards.fulfilled, (state, action) => {
        state.loading = false;

        // For infinite scrolling, append new boards if page > 1
        if (action.meta.arg.page && action.meta.arg.page > 1) {
          // Filter out any duplicates
          const newBoards = action.payload.boards.filter(
            (newBoard: Board) => !state.data.some(existingBoard => existingBoard._id === newBoard._id)
          );
          state.data = [...state.data, ...newBoards];
        } else {
          // First page, replace all data
          state.data = action.payload.boards;
        }

        state.pagination = action.payload.pagination;

        // Check if there are more pages to load
        state.hasNextPage = state.pagination.page < state.pagination.totalPages;
      })
      .addCase(fetchBoards.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch Board by ID
      .addCase(fetchBoardById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBoardById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedBoard = action.payload.board;
      })
      .addCase(fetchBoardById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update Board Status
      .addCase(updateBoardStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateBoardStatus.fulfilled, (state, action) => {
        state.loading = false;
        // Update the board in the state if it exists
        if (state.selectedBoard && state.selectedBoard._id === action.payload.board._id) {
          state.selectedBoard = {
            ...state.selectedBoard,
            ...action.payload.board
          };
        }
        // Update the board in the list if it exists
        const index = state.data.findIndex(board => board._id === action.payload.board._id);
        if (index !== -1) {
          state.data[index] = {
            ...state.data[index],
            ...action.payload.board
          };
        }
      })
      .addCase(updateBoardStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update Board Details
      .addCase(updateBoardDetailsThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateBoardDetailsThunk.fulfilled, (state, action) => {
        state.loading = false;
        // Update the board in the state if it exists
        if (state.selectedBoard && state.selectedBoard._id === action.payload.board._id) {
          state.selectedBoard = {
            ...state.selectedBoard,
            ...action.payload.board
          };
        }
        // Update the board in the list if it exists
        const index = state.data.findIndex(board => board._id === action.payload.board._id);
        if (index !== -1) {
          state.data[index] = {
            ...state.data[index],
            ...action.payload.board
          };
        }
      })
      .addCase(updateBoardDetailsThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Transfer Board
      .addCase(transferBoardThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(transferBoardThunk.fulfilled, (state, action) => {
        state.loading = false;
        // Update the board in the state if it exists
        if (state.selectedBoard && state.selectedBoard._id === action.payload.board._id) {
          state.selectedBoard = {
            ...state.selectedBoard,
            ...action.payload.board
          };
        }
        // Update the board in the list if it exists
        const index = state.data.findIndex(board => board._id === action.payload.board._id);
        if (index !== -1) {
          state.data[index] = {
            ...state.data[index],
            ...action.payload.board
          };
        }
      })
      .addCase(transferBoardThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch Board Members
      .addCase(fetchBoardMembersThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBoardMembersThunk.fulfilled, (state, action) => {
        state.loading = false;
        // Update the board members in the state if the board exists
        if (state.selectedBoard) {
          state.selectedBoard = {
            ...state.selectedBoard,
            members: action.payload.members
          };
        }
      })
      .addCase(fetchBoardMembersThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update Board Member Role
      .addCase(updateBoardMemberRoleThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateBoardMemberRoleThunk.fulfilled, (state, action) => {
        state.loading = false;
        // Update the board members in the state if the board exists
        if (state.selectedBoard) {
          state.selectedBoard = {
            ...state.selectedBoard,
            members: action.payload.members
          };
        }
      })
      .addCase(updateBoardMemberRoleThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Remove Board Member
      .addCase(removeBoardMemberThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeBoardMemberThunk.fulfilled, (state, action) => {
        state.loading = false;
        // Update the board members in the state if the board exists
        if (state.selectedBoard) {
          state.selectedBoard = {
            ...state.selectedBoard,
            members: action.payload.members
          };
        }
      })
      .addCase(removeBoardMemberThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, clearSelectedBoard, resetBoards } = boardSlice.actions;

export default boardSlice.reducer;
