import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

interface Card {
  _id: string;
  title: string;
  description: string;
  board: {
    _id: string;
    title: string;
  };
  actionList: string;
  position: number;
  priority: string;
  dueDate: string | null;
  users: Array<{
    _id: string;
    name: string;
  }>;
  createdAt: string;
  updatedAt: string;
  archived: boolean;
}

interface CardState {
  data: Card[];
  selectedCard: Card | null;
  loading: boolean;
  error: string | null;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  hasNextPage: boolean;
}

export const fetchCards = createAsyncThunk(
  'cards/fetchCards',
  async (params: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
    board?: string;
    priority?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await api.get('/admin/cards', { params });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch cards');
    }
  }
);

export const fetchCardById = createAsyncThunk(
  'cards/fetchCardById',
  async (cardId: string, { rejectWithValue }) => {
    try {
      const response = await api.get(`/admin/cards/${cardId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch card');
    }
  }
);

export const updateCardStatus = createAsyncThunk(
  'cards/updateCardStatus',
  async ({ cardId, status }: { cardId: string; status: string }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/admin/cards/${cardId}/status`, { status });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update card status');
    }
  }
);

const initialState: CardState = {
  data: [],
  selectedCard: null,
  loading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 50,
    totalPages: 1,
  },
  hasNextPage: true,
};

const cardSlice = createSlice({
  name: 'cards',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSelectedCard: (state) => {
      state.selectedCard = null;
    },
    resetCards: (state) => {
      state.data = [];
      state.pagination.page = 1;
      state.hasNextPage = true;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Cards
      .addCase(fetchCards.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCards.fulfilled, (state, action) => {
        state.loading = false;

        // For infinite scrolling, append new cards if page > 1
        if (action.meta.arg.page && action.meta.arg.page > 1) {
          // Filter out any duplicates
          const newCards = action.payload.cards.filter(
            (newCard: Card) => !state.data.some(existingCard => existingCard._id === newCard._id)
          );
          state.data = [...state.data, ...newCards];
        } else {
          // First page, replace all data
          state.data = action.payload.cards;
        }

        state.pagination = action.payload.pagination;

        // Check if there are more pages to load
        state.hasNextPage = state.pagination.page < state.pagination.totalPages;
      })
      .addCase(fetchCards.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch Card by ID
      .addCase(fetchCardById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCardById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedCard = action.payload.card;
      })
      .addCase(fetchCardById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update Card Status
      .addCase(updateCardStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCardStatus.fulfilled, (state, action) => {
        state.loading = false;
        // Update the card in the state if it exists
        if (state.selectedCard && state.selectedCard._id === action.payload.card._id) {
          state.selectedCard = {
            ...state.selectedCard,
            ...action.payload.card
          };
        }
        // Update the card in the list if it exists
        const index = state.data.findIndex(card => card._id === action.payload.card._id);
        if (index !== -1) {
          state.data[index] = {
            ...state.data[index],
            ...action.payload.card
          };
        }
      })
      .addCase(updateCardStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, clearSelectedCard, resetCards } = cardSlice.actions;

export default cardSlice.reducer;
