import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

interface SystemSettings {
  siteName: string;
  siteDescription: string;
  maintenanceMode: boolean;
  allowSignups: boolean;
  requireEmailVerification: boolean;
  maxWorkspacesPerUser: number;
  maxBoardsPerWorkspace: number;
  maxCardsPerBoard: number;
  defaultUserQuota: {
    storage: number;
    members: number;
  };
  emailSettings: {
    fromEmail: string;
    replyToEmail: string;
    sendWelcomeEmail: boolean;
    sendNotificationEmails: boolean;
  };
  integrations: {
    googleAuth: {
      enabled: boolean;
      clientId: string;
      clientSecret: string;
    };
    stripe: {
      enabled: boolean;
      publicKey: string;
      secretKey: string;
      webhookSecret: string;
    };
  };
}

interface SettingsState {
  settings: SystemSettings | null;
  loading: boolean;
  error: string | null;
  updateSuccess: boolean;
}

export const fetchSystemSettings = createAsyncThunk(
  'settings/fetchSystemSettings',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/admin/settings');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch system settings');
    }
  }
);

export const updateSystemSettings = createAsyncThunk(
  'settings/updateSystemSettings',
  async (settingsData: SystemSettings, { rejectWithValue }) => {
    try {
      const response = await api.patch('/admin/settings', settingsData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update system settings');
    }
  }
);

const initialState: SettingsState = {
  settings: null,
  loading: false,
  error: null,
  updateSuccess: false,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearUpdateSuccess: (state) => {
      state.updateSuccess = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch System Settings
      .addCase(fetchSystemSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSystemSettings.fulfilled, (state, action) => {
        state.loading = false;
        state.settings = action.payload.settings;
      })
      .addCase(fetchSystemSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update System Settings
      .addCase(updateSystemSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.updateSuccess = false;
      })
      .addCase(updateSystemSettings.fulfilled, (state, action) => {
        state.loading = false;
        state.settings = action.payload.settings;
        state.updateSuccess = true;
      })
      .addCase(updateSystemSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.updateSuccess = false;
      });
  },
});

export const { clearError, clearUpdateSuccess } = settingsSlice.actions;

export default settingsSlice.reducer;
