import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

interface Subscription {
  _id: string;
  user?: {
    _id: string;
    name: string;
    email: string;
  };
  workspace?: {
    _id: string;
    name: string;
  };
  plan: string;
  status: string;
  startDate: string;
  endDate: string | null;
  trialEndsAt: string | null;
  canceledAt: string | null;
  stripeCustomerId: string;
  stripeSubscriptionId: string;
}

interface SubscriptionPlan {
  _id: string;
  name: string;
  description: string;
  price: number;
  interval: string;
  features: string[];
  isActive: boolean;
  stripePriceId: string;
  createdAt: string;
  updatedAt: string;
}

interface SubscriptionState {
  data: Subscription[];
  plans: SubscriptionPlan[];
  selectedSubscription: Subscription | null;
  loading: boolean;
  error: string | null;
  updateSuccess: boolean;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export const fetchSubscriptions = createAsyncThunk(
  'subscriptions/fetchSubscriptions',
  async (params: { 
    page?: number; 
    limit?: number; 
    status?: string; 
    search?: string;
    plan?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await api.get('/admin/subscriptions', { params });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch subscriptions');
    }
  }
);

export const fetchSubscriptionById = createAsyncThunk(
  'subscriptions/fetchSubscriptionById',
  async (subscriptionId: string, { rejectWithValue }) => {
    try {
      const response = await api.get(`/admin/subscriptions/${subscriptionId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch subscription');
    }
  }
);

export const updateSubscriptionStatus = createAsyncThunk(
  'subscriptions/updateSubscriptionStatus',
  async ({ subscriptionId, status }: { subscriptionId: string; status: string }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/admin/subscriptions/${subscriptionId}/status`, { status });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update subscription status');
    }
  }
);

export const fetchSubscriptionPlans = createAsyncThunk(
  'subscriptions/fetchSubscriptionPlans',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/admin/subscription-plans');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch subscription plans');
    }
  }
);

export const updateSubscriptionPlan = createAsyncThunk(
  'subscriptions/updateSubscriptionPlan',
  async ({ planId, planData }: { planId: string; planData: any }, { rejectWithValue }) => {
    try {
      const response = await api.patch(`/admin/subscription-plans/${planId}`, planData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update subscription plan');
    }
  }
);

const initialState: SubscriptionState = {
  data: [],
  plans: [],
  selectedSubscription: null,
  loading: false,
  error: null,
  updateSuccess: false,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 1,
  },
};

const subscriptionSlice = createSlice({
  name: 'subscriptions',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearUpdateSuccess: (state) => {
      state.updateSuccess = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Subscriptions
      .addCase(fetchSubscriptions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubscriptions.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload.subscriptions;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchSubscriptions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Subscription by ID
      .addCase(fetchSubscriptionById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubscriptionById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedSubscription = action.payload.subscription;
      })
      .addCase(fetchSubscriptionById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update Subscription Status
      .addCase(updateSubscriptionStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.updateSuccess = false;
      })
      .addCase(updateSubscriptionStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.updateSuccess = true;
        // Update the subscription in the state if it exists
        if (state.selectedSubscription && state.selectedSubscription._id === action.payload.subscription._id) {
          state.selectedSubscription = {
            ...state.selectedSubscription,
            ...action.payload.subscription
          };
        }
        // Update the subscription in the list if it exists
        const index = state.data.findIndex(subscription => subscription._id === action.payload.subscription._id);
        if (index !== -1) {
          state.data[index] = {
            ...state.data[index],
            ...action.payload.subscription
          };
        }
      })
      .addCase(updateSubscriptionStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.updateSuccess = false;
      })
      
      // Fetch Subscription Plans
      .addCase(fetchSubscriptionPlans.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubscriptionPlans.fulfilled, (state, action) => {
        state.loading = false;
        state.plans = action.payload.plans;
      })
      .addCase(fetchSubscriptionPlans.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update Subscription Plan
      .addCase(updateSubscriptionPlan.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.updateSuccess = false;
      })
      .addCase(updateSubscriptionPlan.fulfilled, (state, action) => {
        state.loading = false;
        state.updateSuccess = true;
        // Update the plan in the list if it exists
        const index = state.plans.findIndex(plan => plan._id === action.payload.plan._id);
        if (index !== -1) {
          state.plans[index] = {
            ...state.plans[index],
            ...action.payload.plan
          };
        }
      })
      .addCase(updateSubscriptionPlan.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.updateSuccess = false;
      });
  },
});

export const { clearError, clearUpdateSuccess } = subscriptionSlice.actions;

export default subscriptionSlice.reducer;
