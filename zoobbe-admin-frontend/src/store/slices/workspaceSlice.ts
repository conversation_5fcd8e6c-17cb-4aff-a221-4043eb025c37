import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { workspacesAPI } from '../../services/api';

interface WorkspaceUser {
  _id: string;
  name: string;
  email: string;
  username?: string;
}

interface WorkspaceMember {
  user: WorkspaceUser;
  role: string;
}

interface Workspace {
  _id: string;
  name: string;
  shortName: string;
  status?: string;
  members: WorkspaceMember[];
  createdAt: string;
}

interface Pagination {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface WorkspaceState {
  data: Workspace[];
  loading: boolean;
  error: string | null;
  pagination: Pagination;
}

export const fetchWorkspaces = createAsyncThunk(
  'workspaces/fetchWorkspaces',
  async (params: { page?: number; limit?: number; status?: string; search?: string }, { rejectWithValue }) => {
    try {
      const response = await workspacesAPI.getWorkspaces(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch workspaces');
    }
  }
);

export const fetchWorkspaceById = createAsyncThunk(
  'workspaces/fetchWorkspaceById',
  async (workspaceId: string, { rejectWithValue }) => {
    try {
      const response = await workspacesAPI.getWorkspaceById(workspaceId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch workspace');
    }
  }
);

export const updateWorkspaceStatus = createAsyncThunk(
  'workspaces/updateWorkspaceStatus',
  async ({ workspaceId, status, reason }: { workspaceId: string; status: string; reason?: string }, { rejectWithValue }) => {
    try {
      const response = await workspacesAPI.updateWorkspaceStatus(workspaceId, status, reason);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update workspace status');
    }
  }
);

const initialState: WorkspaceState = {
  data: [],
  loading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 1,
  },
};

const workspaceSlice = createSlice({
  name: 'workspaces',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Workspaces
      .addCase(fetchWorkspaces.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWorkspaces.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload.workspaces;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchWorkspaces.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update Workspace Status
      .addCase(updateWorkspaceStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateWorkspaceStatus.fulfilled, (state, action) => {
        state.loading = false;
        // Update the workspace in the state
        const updatedWorkspace = action.payload.workspace;
        const index = state.data.findIndex(workspace => workspace._id === updatedWorkspace._id);
        if (index !== -1) {
          state.data[index] = { ...state.data[index], ...updatedWorkspace };
        }
      })
      .addCase(updateWorkspaceStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError } = workspaceSlice.actions;

export default workspaceSlice.reducer;
