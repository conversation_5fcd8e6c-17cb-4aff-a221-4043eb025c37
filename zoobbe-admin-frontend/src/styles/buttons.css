/* Button styles with proper light/dark mode contrast */

/* Base button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  border-radius: var(--border-radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Button sizes */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: var(--border-radius-sm);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border-radius: var(--border-radius-lg);
}

/* Button variants */
.btn-primary {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border-color: var(--button-primary-bg);
}

.btn-primary:hover {
  background-color: var(--button-primary-hover);
  border-color: var(--button-primary-hover);
}

.btn-secondary {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border-color: var(--button-secondary-bg);
}

.btn-secondary:hover {
  background-color: var(--button-secondary-hover);
  border-color: var(--button-secondary-hover);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.btn-success:hover {
  background-color: color-mix(in srgb, var(--success-color) 90%, black);
  border-color: color-mix(in srgb, var(--success-color) 90%, black);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background-color: color-mix(in srgb, var(--danger-color) 90%, black);
  border-color: color-mix(in srgb, var(--danger-color) 90%, black);
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn-warning:hover {
  background-color: color-mix(in srgb, var(--warning-color) 90%, black);
  border-color: color-mix(in srgb, var(--warning-color) 90%, black);
}

/* Outline button variants */
.btn-outline-primary {
  background-color: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

.btn-outline-secondary {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.btn-outline-secondary:hover {
  background-color: var(--bg-muted);
}

/* Button with icon */
.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon svg,
.btn-icon i {
  margin-right: 0.5rem;
}

.btn-icon-only {
  padding: 0.5rem;
  border-radius: 50%;
}

/* Button group */
.btn-group {
  display: inline-flex;
}

.btn-group .btn:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group .btn:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
