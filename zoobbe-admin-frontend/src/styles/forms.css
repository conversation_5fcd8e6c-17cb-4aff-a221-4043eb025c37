/* Form Elements Styling */

/* Base styles for form elements */
.form-input,
.form-select,
.form-textarea,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="date"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="color"],
select,
textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background-color: #fff;
  color: #1f2937;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Focus states */
.form-input:focus,
.form-select:focus,
.form-textarea:focus,
input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

/* Placeholder styling */
.form-input::placeholder,
input::placeholder,
textarea::placeholder {
  color: #9ca3af;
}

/* Select styling */
.form-select,
select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25rem;
  padding-right: 2.5rem;
}

/* Textarea styling */
.form-textarea,
textarea {
  min-height: 6rem;
  resize: vertical;
}

/* Disabled state */
.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  background-color: #f3f4f6;
  color: #6b7280;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Error state */
.form-input.error,
.form-select.error,
.form-textarea.error,
input.error,
select.error,
textarea.error {
  border-color: #ef4444;
}

.form-input.error:focus,
.form-select.error:focus,
.form-textarea.error:focus,
input.error:focus,
select.error:focus,
textarea.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.25);
}

/* Form label styling */
.form-label,
label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

/* Form group styling */
.form-group {
  margin-bottom: 1.5rem;
}

/* Dark mode styles */
html.dark .form-input,
html.dark .form-select,
html.dark .form-textarea,
html.dark input[type="text"],
html.dark input[type="email"],
html.dark input[type="password"],
html.dark input[type="number"],
html.dark input[type="search"],
html.dark input[type="tel"],
html.dark input[type="url"],
html.dark input[type="date"],
html.dark input[type="datetime-local"],
html.dark input[type="month"],
html.dark input[type="week"],
html.dark input[type="time"],
html.dark input[type="color"],
html.dark select,
html.dark textarea {
  background-color: #1e293b;
  border-color: #3f4655;
  color: #e2e8f0;
}

html.dark .form-input:focus,
html.dark .form-select:focus,
html.dark .form-textarea:focus,
html.dark input:focus,
html.dark select:focus,
html.dark textarea:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.25);
}

html.dark .form-input::placeholder,
html.dark input::placeholder,
html.dark textarea::placeholder {
  color: #94a3b8;
}

html.dark .form-select,
html.dark select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2394a3b8' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

html.dark .form-input:disabled,
html.dark .form-select:disabled,
html.dark .form-textarea:disabled,
html.dark input:disabled,
html.dark select:disabled,
html.dark textarea:disabled {
  background-color: #334155;
  color: #94a3b8;
}

html.dark .form-label,
html.dark label {
  color: #e2e8f0;
}

/* Specific styles for search inputs */
.search-input {
  padding-left: 2.5rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'%3e%3c/path%3e%3c/svg%3e");
  background-position: 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25rem;
}

html.dark .search-input {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2394a3b8'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'%3e%3c/path%3e%3c/svg%3e");
}
