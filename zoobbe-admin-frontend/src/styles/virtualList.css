/* Virtual List Styles */
.infinite-virtual-list {
  width: 100%;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
}

html.dark .infinite-virtual-list {
  border-color: #3f4655 !important;
  background-color: #1e293b !important;
}

.table-header {
  font-weight: bold;
  padding: 12px 0;
  background-color: #f5f5f5;
  border-bottom: 2px solid #e0e0e0;
}

html.dark .table-header {
  background-color: #1e293b !important;
  border-bottom-color: #3f4655 !important;
  color: #e2e8f0 !important;
}

.th {
  flex: 1;
  padding: 0 15px;
  text-align: left;
}

.virtual-row {
  display: flex;
  padding: 12px 0;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

html.dark .virtual-row {
  background-color: #1e293b !important;
  border-bottom-color: #3f4655 !important;
  color: #e2e8f0 !important;
}

.virtual-row:hover {
  background-color: #f9f9f9;
}

html.dark .virtual-row:hover {
  background-color: #2c3e50 !important;
}

.virtual-cell {
  flex: 1;
  padding: 0 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cards-virtual-list,
.workspaces-virtual-list,
.users-virtual-list {
  height: calc(100vh - 250px);
  overflow: hidden;
  border: none;
}

.loading-more {
  text-align: center;
  padding: 10px;
  font-style: italic;
  color: #666;
  background-color: #f9f9f9;
  border-top: 1px solid #e0e0e0;
}

html.dark .loading-more {
  color: #a0aec0 !important;
  background-color: #1e293b !important;
  border-top-color: #3f4655 !important;
}

.empty-list {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f9f9f9;
}

html.dark .empty-list {
  color: #a0aec0 !important;
  background-color: #1e293b !important;
}

/* Accessibility helper class */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* Form elements in dark mode */
html.dark .search-input,
html.dark .filter-select,
html.dark .pagination-select {
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
  border-color: #3f4655 !important;
}

html.dark .search-input::placeholder {
  color: #94a3b8 !important;
}

html.dark .page-title,
html.dark .filters,
html.dark .pagination-controls {
  color: #e2e8f0 !important;
}

/* Status badges in dark mode */
html.dark .status {
  opacity: 0.9 !important;
}

/* Action buttons in dark mode */
html.dark .activate-button {
  background-color: #15803d !important;
  color: white !important;
}

html.dark .activate-button:hover {
  background-color: #16a34a !important;
}

html.dark .suspend-button {
  background-color: #b91c1c !important;
  color: white !important;
}

html.dark .suspend-button:hover {
  background-color: #dc2626 !important;
}